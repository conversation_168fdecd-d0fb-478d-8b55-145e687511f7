import AppLogo from "@/components/_components/AppLogo";
import LoginForm from "@/components/_components/LoginForm";

export default function Login() {
  return (
    <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <AppLogo />
        <h2 className="mt-2 text-center text-xl font-medium leading-9 tracking-tight text-white">
          Welcome to EasyFlow Admin
        </h2>
      </div>

      <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-[480px]">
        <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
          <LoginForm />
        </div>
      </div>
    </div>
  );
}
