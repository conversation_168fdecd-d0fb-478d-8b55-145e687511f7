import FilterDatePicker from "@/components/_components/FilterDatePicker";
import SpinnerOnCard from "@/components/_components/SpinnerOnCard";
import { getAllChartReport } from "@/lib/dashboard.lib";
import { verifySession } from "@/lib/verifySession";
import { Suspense } from "react";
import InstallationChart from "../../../modules/components/dashboard/InstallationChart";
import Statistics from "../../../modules/components/dashboard/Statistics";

export default async function Dashboard(props: { searchParams?: Promise<{ startDate: string; endDate: string }> }) {
  const session = await verifySession();
  if (!session) return null;

  const searchParams = await props.searchParams;

  const { startDate, endDate } = searchParams || {};

  // const series = await cache(() => getAllChartReport(startDate, endDate), [startDate as string, endDate as string], {
  //   revalidate: 3600,
  //   tags: ["statistics"],
  // })();

  const series = await getAllChartReport(startDate, endDate);

  return (
    <div className="flex flex-col gap-4 h-full">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold leading-6 text-gray-900">Statistics</h3>
        <FilterDatePicker popoverAlign="end" />
      </div>

      <Statistics />

      <h3 className="text-base font-semibold leading-6 text-gray-900">Installation</h3>
      <Suspense
        fallback={<SpinnerOnCard />}
        key={JSON.stringify(searchParams) || ""}
      >
        <InstallationChart series={series} />
      </Suspense>
    </div>
  );
}
