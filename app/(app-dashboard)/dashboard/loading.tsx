"use client";

import FilterDatePicker from "@/components/_components/FilterDatePicker";
import SpinnerOnCard from "@/components/_components/SpinnerOnCard";
import StatisticsCardSkeleton from "../../../modules/components/dashboard/StatisticsCardSkeleton";

export default function Loading() {
  return (
    <>
      <div className="flex flex-col gap-4 h-full">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-semibold leading-6 text-gray-900">Statistics</h3>
          <FilterDatePicker popoverAlign="end" />
        </div>

        {/* Statistics */}

        <div className="mt-1 grid grid-cols-2 divide-x divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow lg:grid-cols-6 lg:divide-x lg:divide-y-0">
          {Array.from({ length: 6 }).map((_, index) => (
            <StatisticsCardSkeleton key={index} />
          ))}
        </div>

        {/* Installation */}

        <div className="flex flex-col gap-3">
          <h3 className="text-base font-semibold leading-6 text-gray-900">Installation</h3>
          <SpinnerOnCard />
        </div>
      </div>
    </>
  );
}
