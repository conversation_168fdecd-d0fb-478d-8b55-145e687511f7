import SlideOver from "@/components/_components/SlideOver";
import CouponDetails from "@/modules/components/coupons/CouponDetails";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

export default async function CouponDetailsPage({ params }: { params: Promise<{ id: number }> }) {
  const { id } = await params;
  return (
    <>
      <SlideOver
        open={true}
        title={"Coupon Details"}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <CouponDetails id={id} />
        </Suspense>
      </SlideOver>
    </>
  );
}
