import { getAdmins } from "@/lib/admin.lib";
import { verifySession } from "@/lib/verifySession";
import { Metadata } from "next";
import AdminData from "../../../../modules/components/admins/AdminData";

export const metadata: Metadata = {
  title: "Admin - EasyFlow Admin",
};

export default async function Admins(props: {
  searchParams?: Promise<{
    search: string;
  }>;
}) {
  const session = await verifySession();
  if (!session) return null;

  const searchParams = await props.searchParams;

  // const admins = await unstable_cache(async () => getAdmins(searchParams?.search), [searchParams?.search as string], {
  //   revalidate: 3600,
  //   tags: ["admins"],
  // })();

  const admins = await getAdmins(searchParams?.search);

  return <AdminData admins={admins} />;
}
