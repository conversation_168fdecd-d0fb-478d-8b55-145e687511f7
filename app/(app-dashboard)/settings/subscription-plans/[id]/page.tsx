import SlideOver from "@/components/_components/SlideOver";
import SubscriptionPlanDetails from "@/modules/components/subscription_plans/SubscriptionPlanDetails";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

export default async function SubscriptionPlanDetailsPage({ params }: { params: Promise<{ id: number }> }) {
  const { id } = await params;

  return (
    <>
      <SlideOver
        open={true}
        title={"Plan Details"}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <SubscriptionPlanDetails id={id} />
        </Suspense>
      </SlideOver>
    </>
  );
}
