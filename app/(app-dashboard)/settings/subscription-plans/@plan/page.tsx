import SpinnerOnCard from "@/components/_components/SpinnerOnCard";
import { getCoupons } from "@/lib/coupon.lib";
import { getPlans } from "@/lib/plan.lib";
import { verifySession } from "@/lib/verifySession";
import SubscriptionPlanTable from "@/modules/components/subscription_plans/SubscriptionPlanTable";
import CouponProvider from "@/modules/context/CouponProvider";
import { unstable_cache as cache } from "next/cache";
import { Suspense } from "react";

export default async function SubscriptionPlanPage(props: {
  searchParams: Promise<{ search: string; sortBy: string; sortOrder: string }>;
}) {
  const session = await verifySession();
  if (!session) return null;

  const searchParams = await props.searchParams;

  const plans = await cache(
    async () => getPlans(searchParams?.search, searchParams?.sortBy, searchParams?.sortOrder),
    [searchParams?.search, searchParams?.sortBy, searchParams?.sortOrder],
    {
      revalidate: 3600,
      tags: ["plans"],
    }
  )();

  const coupons = await getCoupons();

  return (
    <CouponProvider coupons={coupons.coupons}>
      <Suspense
        fallback={
          <div className="flex flex-col gap-3 mt-4">
            <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Coupons()</h3>
            <SpinnerOnCard />
          </div>
        }
      >
        <SubscriptionPlanTable plans={plans} />
      </Suspense>
    </CouponProvider>
  );
}
