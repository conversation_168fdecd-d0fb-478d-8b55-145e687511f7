export default async function Stores() {
  return (
    <>
      {/* <Statistics />
      <Suspense
        fallback={
          <div className="flex flex-col gap-3 mt-4">
            <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Stores()</h3>
            <SpinnerOnCard />
          </div>
        }
      >
        <StoresList
          search={search}
          status={status}
          startDate={startDate}
          endDate={endDate}
          sortBy={sortBy}
          sortOrder={sortOrder}
          page={page}
          limit={limit}
        />
      </Suspense> */}
    </>
  );
}
