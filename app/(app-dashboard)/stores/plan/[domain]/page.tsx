import SlideOver from "@/components/_components/SlideOver";
import PlanDetails from "@/modules/components/stores/PlanDetails";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

export default async function PlanDetailsPage({ params }: { params: Promise<{ domain: string }> }) {
  const { domain } = await params;

  return (
    <>
      <SlideOver
        open={true}
        title={`Plan Details - ${domain}`}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <PlanDetails domain={domain} />
        </Suspense>
      </SlideOver>
    </>
  );
}
