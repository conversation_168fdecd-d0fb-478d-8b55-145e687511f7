import SlideOver from "@/components/_components/SlideOver";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";
import StoreDetails from "../../../../modules/components/stores/StoreDetails";

export default async function StoreDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <>
      <SlideOver
        open={true}
        title={"Store Details"}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <StoreDetails id={id} />
        </Suspense>
      </SlideOver>
    </>
  );
}
