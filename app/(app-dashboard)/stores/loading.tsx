"use client";

import StatisticsCardSkeleton from "../../../modules/components/dashboard/StatisticsCardSkeleton";

export default function Loading() {
  return (
    <div className="flex flex-col gap-4 h-full">
      {/* Statistics */}

      <div className="mt-1 grid grid-cols-2 divide-x divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow lg:grid-cols-6 lg:divide-x lg:divide-y-0">
        {Array.from({ length: 6 }).map((_, index) => (
          <StatisticsCardSkeleton key={index} />
        ))}
      </div>

      {/* Stores List */}
      {/* <div className="flex flex-col gap-3">
        <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Stores()</h3>
        <SpinnerOnCard />
      </div> */}
    </div>
  );
}
