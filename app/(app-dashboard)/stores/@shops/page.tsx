import SpinnerOnCard from "@/components/_components/SpinnerOnCard";
import { verifySession } from "@/lib/verifySession";
import { Suspense } from "react";
import Statistics from "../../../../modules/components/dashboard/Statistics";
import StoresList from "../../../../modules/components/stores/StoresList";

export default async function StoresPage(props: {
  searchParams?: Promise<{
    search: string;
    status: string;
    startDate: string;
    endDate: string;
    sortBy: string;
    sortOrder: string;
    page: string;
    limit: string;
  }>;
}) {
  const session = verifySession();
  if (!session) return null;

  const searchParams = await props.searchParams;

  return (
    <>
      <Statistics />
      <Suspense
        fallback={
          <div className="flex flex-col gap-3 mt-4">
            <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Stores()</h3>
            <SpinnerOnCard />
          </div>
        }
      >
        <StoresList searchParams={searchParams} />
      </Suspense>
    </>
  );
}
