import SlideOver from "@/components/_components/SlideOver";
import WebhookDetails from "@/modules/components/stores/WebhookDetails";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

export default async function WebhookDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <>
      <SlideOver
        open={true}
        title={"Webhook Details"}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <WebhookDetails domain={id} />
        </Suspense>
      </SlideOver>
    </>
  );
}
