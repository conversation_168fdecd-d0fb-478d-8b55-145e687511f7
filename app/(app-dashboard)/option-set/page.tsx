export default async function OptionSet() {
  return (
    <></>
    // <Suspense
    //   fallback={
    //     <div className="flex flex-col gap-3 mt-4">
    //       <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Stores()</h3>
    //       <SpinnerOnCard />
    //     </div>
    //   }
    // >
    //   <OptionSetList
    //     search={search}
    //     sortBy={sortBy}
    //     sortOrder={sortOrder}
    //     page={page}
    //     limit={limit}
    //   />
    // </Suspense>
  );
}
