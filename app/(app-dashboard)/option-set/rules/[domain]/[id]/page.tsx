import SlideOver from "@/components/_components/SlideOver";
import OptionRulesDetails from "@/modules/components/optionSet/OptionRulesDetails";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

export default async function OptionRulesPage({ params }: { params: Promise<{ domain: string; id: string }> }) {
  const { domain, id } = await params;

  return (
    <>
      <SlideOver
        open={true}
        title={"Option Rules"}
      >
        <Suspense
          fallback={
            <div className="p-8 flex justify-center items-center">
              <Loader2
                size="2rem"
                className="animate-spin text-center"
              />
            </div>
          }
        >
          <OptionRulesDetails
            domain={domain}
            id={id}
          />
        </Suspense>
      </SlideOver>
    </>
  );
}
