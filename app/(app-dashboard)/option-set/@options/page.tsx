import SpinnerOnCard from "@/components/_components/SpinnerOnCard";
import { verifySession } from "@/lib/verifySession";
import OptionSetList from "@/modules/components/optionSet/OptionSetList";
import { Suspense } from "react";

export default async function OptionSetPage(props: {
  searchParams?: Promise<{
    search: string;
    sortBy: string;
    sortOrder: string;
    page: string;
    limit: string;
    shop: string;
  }>;
}) {
  const session = verifySession();
  if (!session) return null;

  const searchParams = await props.searchParams;

  return (
    <Suspense
      fallback={
        <div className="flex flex-col gap-3 mt-4">
          <h3 className="text-base font-semibold leading-6 text-gray-900">List Of Option Set()</h3>
          <SpinnerOnCard />
        </div>
      }
    >
      <OptionSetList searchParams={searchParams} />
    </Suspense>
  );
}
