import { SidebarProvider } from "@/modules/context/SidebarProvider";
import Sidebar from "../../components/_components/Sidebar";
import Topbar from "../../components/_components/Topbar";

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="h-full">
      <SidebarProvider>
        <Sidebar />
        <div className="lg:pl-72 flex flex-col min-h-full">
          <Topbar />
          <main className="py-4 flex-1 bg-slate-100 h-full flex flex-col justify-between">
            <div className="px-4 sm:px-6 lg:px-8 flex-1 flex flex-col">{children}</div>
            <p className="pt-4 px-4 text-center text-sm text-gray-500">
              EasyFlow Admin | Version: {process.env.NEXT_PUBLIC_APP_VERSION} | Last Update:{" "}
              {process.env.NEXT_PUBLIC_APP_DEPLOYED}
            </p>
          </main>
        </div>
      </SidebarProvider>
    </div>
  );
}
