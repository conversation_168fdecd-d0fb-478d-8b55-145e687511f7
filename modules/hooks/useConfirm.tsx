import ButtonLoadingContent from "@/components/_components/ButtonLoadingContent";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { ReactEventHandler, useState } from "react";
import { useVariant, Variant } from "./useVariant";

type ConfirmAction = {
  label?: string;
  onAction?: ReactEventHandler;
  variant?: string;
  loading?: boolean;
};

type ConfirmAlert = {
  title?: string;
  content?: string;
  confirmAction?: ConfirmAction;
};

export function useConfirm() {
  const [show, setShow] = useState(false);

  const showConfirm = (status = true) => setShow(status);

  const ConfirmAlert = ({
    title = "Are you sure?",
    content = "You won&apos;t be able to revert this!",
    confirmAction = {
      label: "Confirm",
      onAction: () => {},
      variant: "",
      loading: false,
    },
  }: ConfirmAlert) => {
    const { bgColor, textColor, bgHoverColor } = useVariant(confirmAction.variant as Variant, true);

    return (
      <AlertDialog
        open={show}
        onOpenChange={setShow}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{content}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className={cn("h-8")}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className={cn(bgColor, textColor, `hover:${bgHoverColor}`, "h-8")}
              onClick={confirmAction.onAction}
              disabled={confirmAction.loading}
            >
              {confirmAction.loading ? <ButtonLoadingContent /> : confirmAction.label}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return {
    ConfirmAlert,
    showConfirm,
    show,
  };
}
