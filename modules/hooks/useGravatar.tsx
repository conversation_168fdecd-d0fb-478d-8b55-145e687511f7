"use client";

import { getGravatarUrl } from "@/lib/utils";
import { useEffect, useState } from "react";

export const useGravater = (email: String, size: Number = 80) => {
  const [avaterUrl, setAvaterUrl] = useState(
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  );

  useEffect(() => {
    if (email) {
      setAvaterUrl(getGravatarUrl(email, size));
    }
  }, [email, size]);

  return avaterUrl;
};
