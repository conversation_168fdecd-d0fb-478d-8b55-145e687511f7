import { useEffect, useState } from "react";

// Define the valid variants
export type Variant = "default" | "danger" | "warning" | "info" | "success" | "disabled";

const COLOR_CLASS: Record<Variant, string> = {
  default: "text-gray-600",
  danger: "text-red-700",
  warning: "text-yellow-800",
  info: "text-blue-700",
  success: "text-green-700",
  disabled: "text-gray-400 cursor-not-allowed",
};

const BG_CLASS: Record<Variant, string> = {
  default: "bg-gray-100",
  danger: "bg-red-100",
  warning: "bg-yellow-100",
  info: "bg-blue-100",
  success: "bg-green-100",
  disabled: "bg-gray-200 cursor-not-allowed",
};

const DARK_COLOR_CLASS: Record<Variant, string> = {
  default: "text-white",
  danger: "text-white",
  warning: "text-white",
  info: "text-white",
  success: "text-white",
  disabled: "",
};

const DARK_BG_CLASS: Record<Variant, string> = {
  default: "bg-gray-900",
  danger: "bg-red-500",
  warning: "bg-yellow-500",
  info: "bg-blue-500",
  success: "bg-green-500",
  disabled: "",
};

const DARK_HOVER_BG_CLASS: Record<Variant, string> = {
  default: "bg-gray-800",
  danger: "bg-red-600",
  warning: "bg-yellow-600",
  info: "bg-blue-600",
  success: "bg-green-600",
  disabled: "",
};

export function useVariant(variant: Variant = "default", useDarkColor: boolean = false) {
  const [textColor, setTextColor] = useState("");
  const [bgColor, setBgColor] = useState("");
  const [bgHoverColor, setBgHoverColor] = useState("");

  useEffect(() => {
    if (useDarkColor) {
      setTextColor(DARK_COLOR_CLASS[variant]);
      setBgColor(DARK_BG_CLASS[variant]);
      setBgHoverColor(DARK_HOVER_BG_CLASS[variant]);
    } else {
      setTextColor(COLOR_CLASS[variant]);
      setBgColor(BG_CLASS[variant]);
    }
  }, [variant, useDarkColor]);

  return {
    textColor,
    bgColor,
    bgHoverColor,
  };
}
