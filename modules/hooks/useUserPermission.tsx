import { useAuth } from "./useAuth";

export default function useUserPermission(scopes: string[]) {
  const { user } = useAuth();
  if (!user) return { hasReadPermission: false, hasWritePermission: false, hasDeletePermission: false };

  // const permissionScopesArray = JSON.parse(user && user?.scopes) ?? "[]";
  const permissionScopesArray = user?.scopes && typeof user.scopes === "string" ? JSON.parse(user.scopes) : [];

  const hasPermission = (action: string) =>
    scopes?.some((scope) =>
      permissionScopesArray?.some((permission: string) => permission === scope && scope?.includes(action))
    );

  const hasReadPermission = hasPermission("READ");
  const hasWritePermission = hasPermission("WRITE");
  const hasDeletePermission = hasPermission("DELETE");

  return { hasReadPermission, hasWritePermission, hasDeletePermission };
}
