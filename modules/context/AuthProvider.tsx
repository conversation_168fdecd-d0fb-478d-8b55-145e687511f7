"use client";

import { JsonValue } from "@prisma/client/runtime/library";
import { createContext, useEffect, useState } from "react";

interface User {
  id: number;
  name: string;
  email: string;
  type: number;
  scopes: JsonValue;
}

interface IAuthContext {
  user: User | null;
  setUser: (user: User | null) => void;
}

export const AuthContext = createContext<IAuthContext | null>(null);

export function AuthProvider({ children, user: authUser }: Readonly<{ children: React.ReactNode; user: User | null }>) {
  const [user, setUser] = useState<User | null>(authUser);

  useEffect(() => {
    setUser(authUser);
  }, [authUser]);

  return <AuthContext.Provider value={{ user, setUser }}>{children}</AuthContext.Provider>;
}
