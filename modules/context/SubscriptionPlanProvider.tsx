"use client";

import { ISubscriptionPlanData } from "@/types/plans";
import { createContext, useContext } from "react";

export const SubscriptionPlanContext = createContext<ISubscriptionPlanData[]>([]);

export default function SubscriptionPlanProvider({
  children,
  plans,
}: {
  children: React.ReactNode;
  plans: ISubscriptionPlanData[];
}) {
  return <SubscriptionPlanContext.Provider value={plans}>{children}</SubscriptionPlanContext.Provider>;
}

export const useSubscriptionPlans = () => {
  const context = useContext(SubscriptionPlanContext);
  if (!context) {
    throw new Error("useSubscriptionPlans must be used within a SubscriptionPlanProvider");
  }
  return context;
};
