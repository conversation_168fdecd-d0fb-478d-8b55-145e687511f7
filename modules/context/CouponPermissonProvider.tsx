"use client";

import { createContext } from "react";
import useUserPermission from "../hooks/useUserPermission";

interface ICouponPermissionContext {
  hasReadPermission: boolean;
  hasWritePermission: boolean;
  hasDeletePermission: boolean;
}

export const CouponPermissionContext = createContext<ICouponPermissionContext | null>(null);

export function CouponPermissionProvider({ children }: { children: Readonly<React.ReactNode> }) {
  const { hasReadPermission, hasWritePermission, hasDeletePermission } = useUserPermission([
    "SUBSCRIPTION_PLAN_READ",
    "SUBSCRIPTION_PLAN_WRITE",
    "SUBSCRIPTION_PLAN_DELETE",
  ]);

  const permissions = { hasReadPermission, hasWritePermission, hasDeletePermission };

  return <CouponPermissionContext.Provider value={permissions}>{children}</CouponPermissionContext.Provider>;
}
