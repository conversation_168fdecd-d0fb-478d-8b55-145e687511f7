"use client";

import { createContext } from "react";
import useUserPermission from "../hooks/useUserPermission";

interface ISubscriptionPlanProviderContext {
  hasReadPermission: boolean;
  hasWritePermission: boolean;
  hasDeletePermission: boolean;
}

export const SubscriptionPlanPermissionContext = createContext<ISubscriptionPlanProviderContext | null>(null);

export function SubscriptionPlanPermissionProvider({ children }: { children: Readonly<React.ReactNode> }) {
  const { hasReadPermission, hasWritePermission, hasDeletePermission } = useUserPermission([
    "SUBSCRIPTION_PLAN_READ",
    "SUBSCRIPTION_PLAN_WRITE",
    "SUBSCRIPTION_PLAN_DELETE",
  ]);

  const permissions = { hasReadPermission, hasWritePermission, hasDeletePermission };

  return (
    <SubscriptionPlanPermissionContext.Provider value={permissions}>
      {children}
    </SubscriptionPlanPermissionContext.Provider>
  );
}
