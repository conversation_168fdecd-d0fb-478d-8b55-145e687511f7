"use client";

import { ICouponData } from "@/types/coupons";
import { createContext, useContext } from "react";

const CouponContext = createContext<ICouponData[] | null>(null);

export default function CouponProvider({ children, coupons }: { children: React.ReactNode; coupons: ICouponData[] }) {
  return <CouponContext.Provider value={coupons}>{children}</CouponContext.Provider>;
}

export const useCoupons = () => {
  const context = useContext(CouponContext);
  if (!context) {
    throw new Error("useCoupons must be used within a CouponProvider");
  }
  return context;
};
