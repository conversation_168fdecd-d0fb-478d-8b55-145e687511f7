import { z } from "zod";

const packageInfoItemSchema = z.object({
  value: z.boolean(),
  display_name: z.string(),
  name: z.string(),
});

const metaItemSchema = z.object({
  terms: z.string(),
  duration: z.number(),
  description: z.string(),
  coupon_code: z.string().nullable(),
  capped_amount: z
    .object({
      amount: z.number().default(1),
      currencyCode: z.string().default("USD"),
      price_save_description: z.string().optional(),
    })
    .nullable(),
});

const statusSchema = z.preprocess(
  (value) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return value;
  },
  z.boolean({
    required_error: "Status is required",
    invalid_type_error: "Status must be true or false",
  })
);

const testSchema = z.preprocess(
  (value) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return value;
  },
  z.boolean({
    required_error: "Test Charge is required",
    invalid_type_error: "Test charge must be true or false",
  })
);

export const AddPlanSchema = z.object({
  name: z
    .string({
      required_error: "Plan Name is required",
      invalid_type_error: "Plan Name must be a string",
    })
    .min(3, { message: "Plan Name must be at least 3 characters" }),
  slug: z
    .string({
      required_error: "Slug is required",
      invalid_type_error: "Slug must be a string",
    })
    .min(3, { message: "Slug must be at least 3 characters" }),
  type: z.string({
    required_error: "Type is required",
  }),
  interval: z.string({
    required_error: "Interval is required",
  }),
  price: z.coerce.number({
    required_error: "Price is required",
    invalid_type_error: "Price must be number",
  }),
  coupon_id: z.number().optional().nullable(),
  discount: z.coerce.number().optional().default(0),
  discount_type: z.enum(["percent", "amount"]).optional().nullable(),
  final_price: z.number({
    required_error: "Final Price is required",
  }),
  price_save_description: z.string().optional(),
  trial_days: z.coerce.number({
    required_error: "Trial Days is required",
    invalid_type_error: "Trial Days must be number",
  }),
  currency: z.string({
    required_error: "Currency is required",
  }),
  test: testSchema,
  status: statusSchema,
  package_info: z.array(packageInfoItemSchema).optional(),
  meta: metaItemSchema.optional(),
});

export const UpdatePlanSchema = z.object({
  name: z
    .string({
      required_error: "Plan Name is required",
      invalid_type_error: "Plan Name must be a string",
    })
    .min(3, { message: "Plan Name must be at least 3 characters" }),
  slug: z
    .string({
      required_error: "Slug is required",
      invalid_type_error: "Slug must be a string",
    })
    .min(3, { message: "Slug must be at least 3 characters" }),
  type: z.string({
    required_error: "Type is required",
  }),
  interval: z.string({
    required_error: "Interval is required",
  }),
  price: z.coerce.number({
    required_error: "Price is required",
    invalid_type_error: "Price must be number",
  }),
  coupon_id: z.number().optional().nullable(),
  discount: z.coerce.number().optional(),
  discount_type: z.enum(["percent", "amount"]).optional().nullable(),
  final_price: z.number({
    required_error: "Final Price is required",
  }),
  price_save_description: z.string().optional(),
  trial_days: z.coerce.number({
    required_error: "Trial Days is required",
    invalid_type_error: "Trial Days must be number",
  }),
  currency: z.string({
    required_error: "Currency is required",
  }),
  test: testSchema,
  status: statusSchema,
  package_info: z.array(packageInfoItemSchema).optional(),
  meta: metaItemSchema.optional(),
});
