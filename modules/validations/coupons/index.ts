import { z } from "zod";

const statusSchema = z.preprocess(
  (value) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return value;
  },
  z.boolean({
    required_error: "Status is required",
    invalid_type_error: "Status must be true or false",
  })
);

export const AddCouponSchema = z.object({
  name: z
    .string({
      required_error: "Coupon name is required",
      invalid_type_error: "Coupon name must be a string",
    })
    .min(3, { message: "Coupon name must be at least 3 characters" }),
  code: z
    .string({
      required_error: "Coupon code is required",
      invalid_type_error: "Coupon code must be a string",
    })
    .min(6, { message: "Coupon code must be at least 6 characters" }),
  amount: z.coerce.number({
    required_error: "Amount is required",
    invalid_type_error: "Amount must be number",
  }),
  discount_type: z.enum(["percent", "amount"]),
  discount_limit_duration: z.coerce
    .number({
      required_error: "Discount limit is required",
      invalid_type_error: "Discount limit must be number",
    })
    .optional(),
  max_limit: z.coerce.number({
    required_error: "Max limit is required",
    invalid_type_error: "Max limit must be number",
  }),
  start_date: z.coerce.date({
    required_error: "Start date is required",
    invalid_type_error: "Start date must be a date",
  }),
  end_date: z.coerce.date({
    required_error: "End date is required",
    invalid_type_error: "End date must be a date",
  }),

  plans: z.array(z.string().min(1, { message: "Atleast one plan is required" })).optional(),
  status: statusSchema,
});

export const UpdateCouponSchema = z.object({
  name: z.string({
    invalid_type_error: "Coupon name must be a string",
  }),
  code: z
    .string()
    .optional()
    .refine((value) => value === undefined, {
      message: "Coupon code cannot be updated",
    }),
  amount: z
    .number()
    .optional()
    .refine((value) => value === undefined, {
      message: "Amount cannot be updated",
    }),
  discount_type: z
    .number()
    .optional()
    .refine((value) => value === undefined, {
      message: "Discount type cannot be updated",
    }),
  discount_limit_duration: z.coerce
    .number({
      invalid_type_error: "Discount limit must be number",
    })
    .optional(),
  max_limit: z.coerce.number({
    invalid_type_error: "Max limit must be number",
  }),
  start_date: z.coerce.date({
    invalid_type_error: "Start date must be a date",
  }),
  end_date: z.coerce.date({
    invalid_type_error: "End date must be a date",
  }),
  plans: z.array(z.string().min(1, { message: "Plan is required" })),
  status: statusSchema,
});
