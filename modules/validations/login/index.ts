import { z } from "zod";

export const LoginActionSchema = z.object({
  email: z
    .string({
      required_error: "Email is required",
    })
    .regex(new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}/), { message: "Invalid email format" })
    .email(),
  password: z
    .string({
      required_error: "Password is required",
    })
    .min(6, { message: "Password must be at least 6 characters" }),
});
