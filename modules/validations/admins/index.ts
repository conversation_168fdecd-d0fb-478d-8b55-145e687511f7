import { z } from "zod";
import { roles } from "@/enums/admins";

export const AddAdminSchema = z
  .object({
    name: z
      .string({
        required_error: "Name is required",
        invalid_type_error: "Name must be a string",
      })
      .min(3, { message: "Name must be at least 3 characters" }),
    email: z
      .string({
        required_error: "Email is required",
      })
      .regex(new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}/), { message: "Invalid email format" })
      .email(),
    password: z
      .string({
        required_error: "Password is required",
      })
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z
      .string({
        required_error: "Confirm password is required",
      })
      .min(6, { message: "Confirm password must be at least 6 characters" }),
    scopes: z.string().optional(),
    type: z.nativeEnum(roles, { message: "Role is required" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Password do not match",
    path: ["confirmPassword"],
  });

export const UpdateAdminSchema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name must be a string",
    })
    .min(3, { message: "Name must be at least 3 characters" }),
  email: z
    .string({
      required_error: "Email is required",
    })
    .regex(new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}/), { message: "Invalid email format" })
    .email(),
  type: z.nativeEnum(roles, { message: "Role is required" }),
});

export const UpdatePasswordSchema = z
  .object({
    password: z
      .string({
        required_error: "Password is required",
      })
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z
      .string({
        required_error: "Confirm password is required",
      })
      .min(6, { message: "Confirm password must be at least 6 characters" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });
