import { IOptionData } from "@/types/optionSet";
import { formattedDate } from "@/utlis/helpers";
import { omit } from "lodash";

export const optionsDetailsformatter = (data: IOptionData) => {
  const optionData = omit(data, ["title", "values"]);

  const optionDataLabels = {
    type: "Type",
    metaobjectId: "Meta Object Id",
    isRequired: "Required",
    isMultiSelect: "Multi Select",
    productIds: "Product IDs",
    createdAt: "Created At",
  };

  const dataArray =
    data &&
    Object.entries(optionData)?.map(([key, value]) => ({
      key: optionDataLabels[key as keyof typeof optionDataLabels] || key,
      value: value !== null ? (key === "createdAt" ? formattedDate(value.toString()) : value) : "N/A",
    }));

  return dataArray;
};
