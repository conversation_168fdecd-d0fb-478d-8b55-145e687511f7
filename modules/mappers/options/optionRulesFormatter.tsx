import { IOptionRules } from "@/types/optionSet";
import { omit } from "lodash";

export const optionRulesformatter = (data: IOptionRules) => {
  const optionData = omit(data, ["name"]);

  const optionRuleLabels = {
    conditions: "Conditions",
    actions: "Actions",
    isAllConditionsRequired: "All Condtion Required",
  };

  const dataArray =
    data &&
    Object.entries(optionData)?.map(([key, value]) => ({
      key: optionRuleLabels[key as keyof typeof optionRuleLabels] || key,
      value: JSON.stringify(value, null, 2),
    }));

  return dataArray;
};
