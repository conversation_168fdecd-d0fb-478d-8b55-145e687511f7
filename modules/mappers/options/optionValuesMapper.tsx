import { IOptionValues } from "@/types/optionSet";
import { omit } from "lodash";

export const optionsValuesformatter = (data: IOptionValues) => {
  const optionValuesData = omit(data, ["title"]);
  const optionValueLabels = {
    price: "Price",
    isDefault: "Default",
    addonType: "Addon Type",
    addonProduct: "Addon Product",
  };

  const dataArray =
    data &&
    Object.entries(optionValuesData)?.map(([key, value]) => ({
      key: optionValueLabels[key as keyof typeof optionValueLabels] || key,
      value: JSON.stringify(value, null, 2),
    }));

  return dataArray;
};
