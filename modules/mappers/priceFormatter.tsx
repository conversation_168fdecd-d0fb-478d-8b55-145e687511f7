import { ReactNode } from "react";

type TFormatter = {
  value: ReactNode;
  underline?: boolean;
  type?: string;
};

const PriceFormatter = ({ value, underline = false, type = "amount" }: TFormatter) => {
  return (
    <span className={`mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0 ${underline ? "underline" : null}`}>
      {value === null ? "-" : type === "amount" ? `${value}` : `${value}%`}
    </span>
  );
};

export default PriceFormatter;
