import { Badge } from "@/components/ui/badge";
import { ICouponData } from "@/types/coupons";
import { formattedDate } from "@/utlis/helpers";
import PlainTextFormatter from "@/utlis/plainTextFormatter";

export const couponDetailsFormatter = (data: ICouponData) => {
  return [
    {
      key: "id",
      label: "ID",
      content: <PlainTextFormatter value={data?.id} />,
    },

    {
      key: "name",
      label: "Name",
      content: <PlainTextFormatter value={data?.name} />,
    },

    {
      key: "code",
      label: "Code",
      content: <PlainTextFormatter value={data?.code} />,
    },

    {
      key: "discount_type",
      label: "Discount Type",
      content: <PlainTextFormatter value={data?.discount_type} />,
    },
    {
      key: "amount",
      label: "Amount",
      content: <PlainTextFormatter value={data?.amount} />,
    },
    {
      key: "discount_limit_duration",
      label: "Discount Limit Duration",
      content: <PlainTextFormatter value={data?.discount_limit_duration} />,
    },
    {
      key: "start_date",
      label: "Start Date",
      content: <PlainTextFormatter value={formattedDate(data?.start_date)} />,
    },
    {
      key: "end_date",
      label: "End Date",
      content: <PlainTextFormatter value={formattedDate(data?.end_date)} />,
    },
    {
      key: "max_limit",
      label: "Max Limit",
      content: <PlainTextFormatter value={data?.max_limit} />,
    },
    {
      key: "reedem_count",
      label: "Reedem Count",
      content: <PlainTextFormatter value={data?.redeem_count} />,
    },
    {
      key: "plans",
      label: "Plans",
      content: (
        <div className="flex gap-2 flex-wrap">
          {data?.plans?.map((item, index) => (
            <Badge
              variant="default"
              key={index}
            >
              Plan id: {item}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      content: (
        <div className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.status === true ? (
            <Badge
              variant="success"
              size="xsm"
              textTransform="capital"
            >
              active
            </Badge>
          ) : (
            <Badge
              variant="danger"
              size="xsm"
              textTransform="capital"
            >
              inactive
            </Badge>
          )}
        </div>
      ),
    },

    {
      key: "created_at",
      label: "Created At",
      content: <PlainTextFormatter value={formattedDate(data?.created_at)} />,
    },

    {
      key: "updated_at",
      label: "Updated At",
      content: <PlainTextFormatter value={formattedDate(data?.updated_at)} />,
    },
  ];
};
