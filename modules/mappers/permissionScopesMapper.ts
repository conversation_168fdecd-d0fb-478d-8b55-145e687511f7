import { TPermissionScopes } from "@/types/admin";

const permissionLabel: Record<string, string> = {
  DASHBOARD: "Dashboard",
  STORE: "Stores",
  OPTION_SET: "Option Set",
  ADMIN: "Settings: Admins",
  SUBSCRIPTION_PLAN: "Settings: Subscription Plans",
  COUPON: "Settings: Coupons",
};

export const permissionScopes = (permissions: TPermissionScopes) => {
  const grouped: Record<string, { name: string; features: string[] }> = {};

  for (const key in permissions) {
    if (permissions.hasOwnProperty(key)) {
      const group = Object.keys(permissionLabel).find((labelKey) => key.startsWith(labelKey)) || "Unknown";

      if (!grouped[group]) {
        grouped[group] = {
          name: permissionLabel[group] || "Unknown",
          features: [],
        };
      }

      grouped[group].features.push(key);
    }
  }
  return Object.values(grouped);
};
