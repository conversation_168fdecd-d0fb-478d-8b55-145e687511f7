import { Badge } from "@/components/ui/badge";
import { IShopData } from "@/types/stores";
import { formattedDate } from "@/utlis/helpers";
import PlainTextFormatter from "@/utlis/plainTextFormatter";

const storesDetailsViewFormatter = (data: IShopData) => {
  return [
    {
      key: "name",
      label: "Name",
      content: <PlainTextFormatter value={data?.name} />,
    },
    {
      key: "email",
      label: "Email",
      content: <PlainTextFormatter value={data?.email} />,
    },

    {
      key: "domain",
      label: "Domain",
      content: <PlainTextFormatter value={data?.domain} />,
    },
    {
      key: "url",
      label: "URL",
      content: <PlainTextFormatter value={data?.url} />,
    },

    {
      key: "globalId",
      label: "Global Id",
      content: <PlainTextFormatter value={data?.globalId} />,
    },

    {
      key: "billingConfigKey",
      label: "Billing Config Key",
      content: <PlainTextFormatter value={data?.billingConfigKey} />,
    },

    {
      key: "crispSessionId",
      label: "Crisp Session Id",
      content: <PlainTextFormatter value={data?.crispSessionId} />,
    },

    {
      key: "currencyCode",
      label: "Currency Code",
      content: <PlainTextFormatter value={data?.currencyCode} />,
    },

    {
      key: "isAddonsFree",
      label: "Addons Free",
      content: <PlainTextFormatter value={data?.isAddonsFree?.toString()} />,
    },

    {
      key: "isExtensionEnabled",
      label: "Extension Enabled",
      content: <PlainTextFormatter value={data?.isExtensionEnabled?.toString()} />,
    },

    {
      key: "isPartnerDevelopment",
      label: "Partner Development",
      content: <PlainTextFormatter value={data?.isPartnerDevelopment?.toString()} />,
    },

    {
      key: "created_at",
      label: "Created At",
      content: <PlainTextFormatter value={formattedDate(data?.createdAt)} />,
    },

    {
      key: "isSubscriptionActive",
      label: "Subscription Status",
      content: (
        <p className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.status === "ACTIVE" ? (
            <Badge
              variant="success"
              textTransform="capital"
              size="xsm"
            >
              active
            </Badge>
          ) : (
            <Badge
              variant="warning"
              textTransform="capital"
              size="xsm"
            >
              without plan
            </Badge>
          )}
        </p>
      ),
    },

    {
      key: "status",
      label: "Status",
      content: (
        <p className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.status === "ACTIVE" ? (
            <Badge
              variant="success"
              textTransform="capital"
              size="xsm"
            >
              active
            </Badge>
          ) : (
            <Badge
              variant="danger"
              textTransform="capital"
              size="xsm"
            >
              uninstalled
            </Badge>
          )}
        </p>
      ),
    },
  ];
};

export default storesDetailsViewFormatter;
