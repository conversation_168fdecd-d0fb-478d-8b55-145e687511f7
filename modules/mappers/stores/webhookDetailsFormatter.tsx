import { IWebhookData } from "@/types/stores";
import { formattedDateTime } from "@/utlis/helpers";

const webhookDetailsFormatter = (data: IWebhookData) => {
  if (!data) return [];

  const webhookMappings = {
    id: { label: "Id", value: data.id },
    shop_domain: { label: "Shop Domain", value: data.shop_domain },
    wh_subs_id: { label: "Webhook Subscription Id", value: data.wh_subs_id },
    delivery_method: { label: "Delivery Method", value: data.delivery_method },
    response: { label: "Response", value: data.response },
    created_at: { label: "Created At", value: formattedDateTime(data.created_at.toISOString()) },
    updated_at: { label: "Updated At", value: formattedDateTime(data.updated_at.toISOString()) },
  };

  return Object.values(webhookMappings).map(({ label, value }) => ({
    key: label,
    value: value ?? "N/A",
  }));
};

export default webhookDetailsFormatter;
