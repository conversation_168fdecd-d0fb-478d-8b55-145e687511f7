import { IBillingdata } from "@/types/stores";

export const BillingAddressFormatter = ({ data }: { data: IBillingdata }) => {
  return (
    <div>
      <p className="py-0.5">
        ID:
        <span className="text-sm leading-6 text-gray-700 ">{data?.id}</span>
      </p>
      <p className="py-0.5">
        Phone: <span className="text-sm leading-6 text-gray-700 ">{data?.phone}</span>
      </p>
      <p className="py-0.5">
        Address:
        <span className="text-sm leading-6 text-gray-700 ">
          {data?.address1}, {data?.address2}
        </span>
      </p>
      <p className="py-0.5">
        City: <span className="text-sm leading-6 text-gray-700 ">{data?.city}</span>
      </p>
      <p className="py-0.5">
        Company: <span className="text-sm leading-6 text-gray-700 ">{data?.company}</span>
      </p>
      <p className="py-0.5">
        Country:
        <span className="text-sm leading-6 text-gray-700 ">
          {data?.country}, {data?.countryCodeV2}
        </span>
      </p>
      <p className="py-0.5">
        Formatted Area:
        <span className="text-sm leading-6 text-gray-700 ">{data?.formattedArea}</span>
      </p>
      <p className="py-0.5">
        Lattitude:
        <span className="text-sm leading-6 text-gray-700 ">{data?.latitude}</span>
      </p>
      <p className="py-0.5">
        Longitude:
        <span className="text-sm leading-6 text-gray-700 ">{data?.longitude}</span>
      </p>
      <p className="py-0.5">
        Province:
        <span className="text-sm leading-6 text-gray-700 ">
          {data?.province}, {data?.provinceCode}
        </span>
      </p>
      <p className="py-0.5">
        Zip: <span className="text-sm leading-6 text-gray-700 ">{data?.zip}</span>
      </p>
    </div>
  );
};
