export type RoleName = "SUPER_ADMIN" | "ADMIN" | "TESTING" | "MARKETING" | "SUPPORT" | "OTHER";

type ScopeType = "adminPermissons" | "otherPermission";

type RolePermissions = {
  [key in RoleName]: {
    type: number;
    permission: string[];
  };
};

export const scopes = {
  DASHBOARD: {
    adminPermissons: ["DASHBOARD_READ"],
    otherPermission: ["DASHBOARD_READ"],
  },
  STORE: {
    adminPermissons: ["STORE_READ", "STORE_WRITE", "STORE_DELETE"],
    otherPermission: ["STORE_READ"],
  },
  OPTION_SET: {
    adminPermissons: ["OPTION_SET_READ", "OPTION_SET_WRITE"],
    otherPermission: ["OPTION_SET_READ"],
  },
  ADMIN: {
    adminPermissons: ["ADMIN_READ", "ADMIN_WRITE", "ADMIN_DELETE"],
    otherPermission: ["ADMIN_READ"],
  },
  SUBSCRIPTION_PLAN: {
    adminPermissons: ["SUBSCRIPTION_PLAN_READ", "SUBSCRIPTION_PLAN_WRITE", "SUBSCRIPTION_PLAN_DELETE"],
    otherPermission: ["SUBSCRIPTION_PLAN_READ"],
  },
  COUPON: {
    adminPermissons: ["COUPON_READ", "COUPON_WRITE", "COUPON_DELETE"],
    otherPermission: ["COUPON_READ"],
  },
};

const buildPermissions = (type: ScopeType) => {
  return Object.values(scopes).flatMap((scope) => scope[type]);
};

const adminRolesPermissions = buildPermissions("adminPermissons");
const otherRolesPermissons = buildPermissions("otherPermission");

export const ROLE_PERMISSIONS: RolePermissions = {
  SUPER_ADMIN: {
    type: 1,
    permission: adminRolesPermissions,
  },
  ADMIN: {
    type: 2,
    permission: adminRolesPermissions,
  },

  TESTING: {
    type: 3,
    permission: otherRolesPermissons,
  },
  MARKETING: {
    type: 4,
    permission: otherRolesPermissons,
  },
  SUPPORT: {
    type: 5,
    permission: otherRolesPermissons,
  },
  OTHER: {
    type: 9,
    permission: otherRolesPermissons,
  },
};
