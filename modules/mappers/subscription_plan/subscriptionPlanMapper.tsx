import { Badge } from "@/components/ui/badge";
import { PLAN_INTERVALS } from "@/config/subscription-plans";
import { ISubscriptionPlanData } from "@/types/plans";
import { formattedDate } from "@/utlis/helpers";
import PlainTextFormatter from "@/utlis/plainTextFormatter";
import PriceFormatter from "../priceFormatter";

export const subcriptionPlanDetailsFormatter = (data: ISubscriptionPlanData) => {
  const intervals = Object.entries(PLAN_INTERVALS).map(([, value]) => ({
    value: value.value,
    label: value.label,
  }));

  const metaLabels = {
    terms: "Terms",
    capped_amount: "Capped amount",
    description: "Description",
    duration: "Duration",
    isSpecial: "Is special",
    trialDays: "Trial days",
    price_save_description: "Price Save Description",
  };

  const metaArray =
    data?.meta &&
    Object.entries(data?.meta).map(([key, value]) => ({
      key: metaLabels[key as keyof typeof metaLabels] || key,
      value,
    }));

  return [
    {
      key: "id",
      label: "ID",
      content: <PlainTextFormatter value={data?.id} />,
    },

    {
      key: "name",
      label: "Name",
      content: <PlainTextFormatter value={data?.name} />,
    },

    {
      key: "slug",
      label: "Slug",
      content: <PlainTextFormatter value={data?.slug} />,
    },
    {
      key: "type",
      label: "Type",
      content: (
        <div className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.type === "PRO" ? (
            <Badge
              variant="info"
              size="xsm"
              textTransform="capital"
            >
              Pro
            </Badge>
          ) : (
            <Badge
              variant="default"
              size="xsm"
              textTransform="capital"
            >
              Free
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "intetval",
      label: "Interval",
      content: (
        <PlainTextFormatter
          value={intervals.map((interval, idx) => {
            if (data?.interval?.includes(interval?.value)) {
              return <p key={idx}>{interval?.label}</p>;
            }
            return null;
          })}
        />
      ),
    },

    {
      key: "price",
      label: "Price",
      content: <PriceFormatter value={data?.price} />,
    },

    {
      key: "coupon _id",
      label: "Coupon Id",
      content: <PlainTextFormatter value={data?.coupon_id} />,
    },
    {
      key: "discount",
      label: "Discount",
      content: (
        <PriceFormatter
          value={data?.discount}
          type={data?.discount_type}
        />
      ),
    },
    {
      key: "discount_type",
      label: "Discount Type",
      content: <PlainTextFormatter value={data?.discount_type ? data?.discount_type : "-"} />,
    },
    {
      key: "final_price",
      label: "Final Price",
      content: <PriceFormatter value={data?.final_price} />,
    },
    {
      key: "currency",
      label: "Currency",
      content: <PlainTextFormatter value={data?.currency} />,
    },
    {
      key: "trial_days",
      label: "Trial Days",
      content: <PlainTextFormatter value={data?.trial_days} />,
    },
    {
      key: "test",
      label: "Test Charge",
      content: (
        <div className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.test === true ? (
            <Badge
              variant="success"
              size="xsm"
              textTransform="capital"
            >
              active
            </Badge>
          ) : (
            <Badge
              variant="danger"
              size="xsm"
              textTransform="capital"
            >
              inactive
            </Badge>
          )}
        </div>
      ),
    },

    {
      key: "status",
      label: "Status",
      content: (
        <div className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
          {data?.status === true ? (
            <Badge
              variant="success"
              size="xsm"
              textTransform="capital"
            >
              active
            </Badge>
          ) : (
            <Badge
              variant="danger"
              size="xsm"
              textTransform="capital"
            >
              inactive
            </Badge>
          )}
        </div>
      ),
    },

    {
      key: "features",
      label: "Features",
      content: (
        <div className="flex gap-2 flex-wrap">
          {data.package_info?.map((feature, index) => (
            <div key={index}>
              <label>
                <input
                  type="checkbox"
                  name={feature.name}
                  className="hidden"
                  readOnly
                  checked={feature.value}
                />

                <Badge variant={feature?.value ? "success" : "default"}>{feature.display_name}</Badge>
              </label>
            </div>
          ))}
        </div>
      ),
    },

    {
      key: "meta",
      label: "Meta",
      content: (
        <div>
          {data?.meta ? (
            <div className="">
              {metaArray?.map(({ key, value }) => (
                <div
                  key={key}
                  className="flex gap-2 text-sm leading-6 text-gray-700 py-1"
                >
                  <span className="">{key}:</span>
                  <span>{value?.toString()}</span>
                </div>
              ))}
            </div>
          ) : (
            " - "
          )}
        </div>
      ),
    },

    {
      key: "created_at",
      label: "Created At",
      content: <PlainTextFormatter value={formattedDate(data?.created_at)} />,
    },

    {
      key: "updated_at",
      label: "Updated At",
      content: <PlainTextFormatter value={formattedDate(data?.updated_at)} />,
    },
  ];
};
