import AccordionContentList from "@/components/_components/AccordionContentList";
import AccordionWrapper from "@/components/_components/AccordionWrapper";
import datastore from "@/lib/datastore";
import { optionsDetailsformatter } from "@/modules/mappers/options/optionsMapper";
import { optionsValuesformatter } from "@/modules/mappers/options/optionValuesMapper";
import { IOptionData } from "@/types/optionSet";
import { isEmpty } from "lodash";

export default async function OptionDetails({ domain, id }: { domain: string; id: string }) {
  const options: IOptionData[] = await datastore.option.getOptionDetails(domain, id);

  return (
    <>
      {options?.map((option, idx) => {
        const optionData = optionsDetailsformatter(option);

        return (
          <div
            key={idx}
            className="flex flex-col gap-4 py-4"
          >
            <AccordionWrapper
              title={`Option: ${option?.title}`}
              background="bg-gray-50"
            >
              {isEmpty(option) ? (
                "No Data Available"
              ) : (
                <>
                  <AccordionContentList
                    key={idx}
                    data={optionData}
                  />

                  <div>
                    <h2 className="ml-4 mt-2 text-sm font-medium leading-6 text-gray-900">Values: </h2>
                    {option?.values?.map((value, idx) => {
                      const optionValues = optionsValuesformatter(value);
                      return (
                        <div
                          key={idx}
                          className="flex flex-col gap-4 py-4"
                        >
                          <AccordionWrapper
                            title={value?.title}
                            margin="mx-2"
                          >
                            {isEmpty(value) ? (
                              "No Data Available"
                            ) : (
                              <>
                                <AccordionContentList
                                  key={idx}
                                  data={optionValues}
                                />
                              </>
                            )}
                          </AccordionWrapper>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </AccordionWrapper>
          </div>
        );
      })}
    </>
  );
}
