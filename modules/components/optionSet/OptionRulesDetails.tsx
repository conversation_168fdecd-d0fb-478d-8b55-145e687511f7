import AccordionContentList from "@/components/_components/AccordionContentList";
import AccordionWrapper from "@/components/_components/AccordionWrapper";
import datastore from "@/lib/datastore";
import { optionRulesformatter } from "@/modules/mappers/options/optionRulesFormatter";
import { IOptionRules } from "@/types/optionSet";
import { isEmpty } from "lodash";

export default async function OptionRulesDetails({ domain, id }: { domain: string; id: string }) {
  const rules: IOptionRules[] = await datastore.option.getOptionRules(domain, id);
  if (isEmpty(rules)) return "No Data Found";

  return (
    <>
      {rules?.map((rule, idx) => {
        const optionRules = optionRulesformatter(rule);

        return (
          <div
            key={idx}
            className="flex flex-col gap-4 py-4"
          >
            <AccordionWrapper
              background="bg-gray-50"
              title={`Name: ${rule.name}`}
            >
              <AccordionContentList
                key={idx}
                data={optionRules}
              />
            </AccordionWrapper>
          </div>
        );
      })}
    </>
  );
}
