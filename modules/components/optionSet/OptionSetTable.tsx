"use client";

import NoData from "@/components/_components/NoData";
import Pagination from "@/components/_components/Pagination";
import { IOptionSet, IShopNames } from "@/types/optionSet";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import OptionSetSearchFilter from "./OptionSetSearchFilter";
import OptionSetTableBody from "./OptionSetTableBody";

type TOptionSetTable = {
  options: IOptionSet;
  shops: IShopNames[];
};

export default function OptionSetTable({ options, shops }: TOptionSetTable) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const pageNumber = Number(searchParams.get("page"));
  const pageLimit = Number(searchParams.get("limit"));
  const sortBy = searchParams.get("sortBy");
  const sortOrder = searchParams.get("sortOrder");

  const [currentPage, setCurrentPage] = useState<number>(pageNumber || 1);
  const [limit, setLimit] = useState<number>(pageLimit || 20);
  const [shopName, setShopName] = useState<string | null>(null);
  const [sortItem, setSortItem] = useState(sortBy);
  const [orderStatus, setOrderStatus] = useState(sortOrder || "ASC");

  const handleSort = (item: string) => {
    if (item === sortItem) {
      setOrderStatus(orderStatus === "ASC" ? "DESC" : "ASC");
    } else {
      setOrderStatus("ASC");
      setSortItem(item);
    }
  };

  const handleShop = (shop: string) => {
    setShopName(shop);
    setCurrentPage(1);
  };

  const handleClearQuery = () => {
    setCurrentPage(1);
    router.replace("/option-set");
    // setSearchTerm("");
    setLimit(20);
    setSortItem(null);
  };

  const debouncedSearchParams = useDebouncedCallback(({ shopName }) => {
    const currentSearchParams = new URLSearchParams(searchParams.toString());

    if (shopName) {
      currentSearchParams.set("shop", shopName);
    } else {
      currentSearchParams.delete("shop");
    }

    router.push(`${pathname}?${currentSearchParams.toString()}`);
  }, 1000);

  useEffect(() => {
    const searchString = new URLSearchParams(searchParams.toString());

    if (currentPage !== 1) {
      searchString.set("page", currentPage.toString());
    } else {
      searchString.delete("page");
    }

    if (sortItem) {
      searchString.set("sortBy", sortItem);
      searchString.set("sortOrder", orderStatus);
    } else {
      searchString.delete("sortBy");
      searchString.delete("sortOrder");
    }

    // if (limit === 20) {
    //   searchString.delete("limit");
    // }

    router.push(`${pathname}?${searchString.toString()}`);
  }, [pathname, router, searchParams, sortItem, orderStatus, currentPage, limit]);

  useEffect(() => {
    debouncedSearchParams({ shopName });
  }, [shopName, debouncedSearchParams]);

  return (
    <>
      <h2 className="text-base font-semibold leading-6 text-gray-900 mt-6">
        List of Option Set ({options?.totalCount})
      </h2>
      <section className="my-4 w-full mt-2">
        <OptionSetSearchFilter
          onClearQuery={handleClearQuery}
          onShop={handleShop}
          shops={shops}
        />
        <div className="bg-white shadow sm:rounded-lg overflow-hidden">
          {options.optionSetCount === 0 ? (
            <NoData />
          ) : (
            <>
              <OptionSetTableBody
                options={options}
                onSort={handleSort}
                currentPage={currentPage}
                limit={limit}
              />
              <Pagination
                count={options?.totalCount}
                shopCount={options?.optionSetCount}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                limit={limit}
              />
            </>
          )}
        </div>
      </section>
    </>
  );
}
