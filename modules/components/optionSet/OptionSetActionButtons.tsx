import ComboBoxWrapper from "@/components/_components/ComboBoxWrapper";
import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";

type TActionButtons = {
  onShop: (shop: string) => void;
  shops: { label: string; value: string }[];
  onClearQuery: () => void;
};

export default function OptionSetActionButtons({ onShop, onClearQuery, shops }: TActionButtons) {
  const searchParams = useSearchParams();
  const shop = searchParams.get("shop");

  return (
    <div className="w-full flex flex-col items-start md:flex-row space-y-2 md:space-y-0 md:space-x-3 flex-shrink-0 px-2 pb-2">
      <div className="flex gap-3 items-start w-full relative">
        <ComboBoxWrapper
          title="Shop"
          items={shops}
          onSelect={onShop}
          searchOption={true}
          inputWidth="150px"
          popoverWidth="210px"
          value={shop ?? undefined}
        />

        <Button
          onClick={onClearQuery}
          size="sm"
          variant="secondary"
        >
          Clear All
        </Button>
      </div>
    </div>
  );
}
