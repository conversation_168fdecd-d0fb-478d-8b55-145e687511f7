import datastore from "@/lib/datastore";
import { IOptionSetParams, IShopNames } from "@/types/optionSet";
import { unstable_cache as cache } from "next/cache";
import OptionSetTable from "./OptionSetTable";

export default async function OptionSetList({ searchParams }: { searchParams?: IOptionSetParams }) {
  const { search, sortBy, sortOrder, page, limit, shop } = searchParams || {};

  const optionSet = await cache(
    () => datastore.option.getOptionSet(search, sortBy, sortOrder, page, limit, shop),
    [search, sortBy, sortOrder, page, limit, shop] as string[],
    {
      revalidate: 3600,
      tags: ["optionSet"],
    }
  )();

  const shopNames = await cache(() => datastore.shop.getAllShopNames(), [], {
    revalidate: 3600,
    tags: ["shops"],
  })();

  return (
    <OptionSetTable
      options={JSON.parse(JSON.stringify(optionSet))}
      shops={shopNames as IShopNames[]}
    />
  );
}
