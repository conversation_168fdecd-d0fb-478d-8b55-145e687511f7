import { TableDropdownActions, TTableDropdownActionOrSeperator } from "@/components/_components/TableDropdownActions";
import { IOptionSetData } from "@/types/optionSet";
import { calculateDynamicId, formattedDate } from "@/utlis/helpers";
import { InfoIcon, ShieldEllipsis } from "lucide-react";

export default function OptionSetTableData({
  option,
  index,
  currentPage = 1,
  limit = 20,
}: {
  option: IOptionSetData;
  index: number;
  currentPage: number;
  limit: number;
}) {
  const actions: TTableDropdownActionOrSeperator[] = [
    {
      title: "Option Details",
      Icon: InfoIcon,
      url: `/option-set/${option?.domain}/${option.id}`,
      variant: "default",
    },
    {
      title: "Option Rules",
      Icon: ShieldEllipsis,
      url: `/option-set/rules/${option?.domain}/${option.id}`,
      variant: "warning",
    },
  ];

  return (
    <tr
      key={option.domain}
      className="border-b dark:border-gray-700"
    >
      <td className="px-2 py-1">{calculateDynamicId(index, currentPage, limit)}</td>
      <td className="px-2 py-1">{option?.title}</td>
      <td className="px-2 py-1">{option?.optionIds?.length}</td>
      <td className="px-2 py-1">{option?.productIds?.length}</td>
      <td className="px-2 py-1">{option?.productSelectionMethod}</td>
      <td className="px-2 py-1">{option?.rank}</td>
      <td className="px-2 py-1">{formattedDate(option?.createdAt)}</td>

      {/* Action Button */}

      <td className="px-2 py-1 text-center">
        <TableDropdownActions
          label="Action"
          actions={actions}
        />
      </td>
    </tr>
  );
}
