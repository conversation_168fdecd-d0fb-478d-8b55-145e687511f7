import SortableColumn from "@/components/_components/SortableColumn";
import { IOptionSet, IOptionSetData } from "@/types/optionSet";
import OptionSetTableData from "./OptionSetTableData";

export default function OptionSetTableBody({
  options,
  onSort,
  currentPage,
  limit,
}: {
  options: IOptionSet;
  onSort: (value: string) => void;
  currentPage: number;
  limit: number;
}) {
  const tableHeaders = [
    { key: "index", heading: "#", isSortable: false },
    { key: "title", heading: "Title", isSortable: true, extraClass: "cursor-pointer" },
    { key: "total_options", heading: "Total Options", isSortable: false },
    { key: "total_products", heading: "Total Products", isSortable: false },
    { key: "product_selection_method", heading: "Product Selection Method", isSortable: false },
    { key: "rank", heading: "Rank", isSortable: false },
    { key: "createdAt", heading: "Created At", isSortable: true, extraClass: "cursor-pointer" },
    { key: "actions", heading: "Actions", isSortable: false, extraClass: "text-center w-28" },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        {/* Table Head */}
        <thead className="text-xs text-gray-700 uppercase border-b-2 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-400">
          <tr>
            {tableHeaders.map((header) => (
              <th
                key={header.key}
                scope="col"
                className={`px-2 py-2 ${header?.extraClass}`}
              >
                {header.isSortable ? (
                  <SortableColumn
                    heading={header.heading}
                    name={header.key}
                    onSort={onSort}
                  />
                ) : (
                  header.heading
                )}
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {options?.optionSet?.map((option: IOptionSetData, index: number) => (
            <OptionSetTableData
              option={option}
              key={index}
              index={index}
              currentPage={currentPage}
              limit={limit}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}
