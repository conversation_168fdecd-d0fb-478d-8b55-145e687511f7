import SearchBar from "@/components/_components/SearchBar";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ListFilter } from "lucide-react";
import { useState } from "react";
import OptionSetActionButtons from "./OptionSetActionButtons";

type TOptionSearchFilter = {
  onClearQuery: () => void;
  shops: { label: string; value: string }[];
  onShop: (shop: string) => void;
};

export default function OptionSetSearchFilter({ onClearQuery, shops, onShop }: TOptionSearchFilter) {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [tableFilterOpen, setTableFilterOpen] = useState<boolean>(false);

  const handleManageModal = () => {
    setShowModal(!showModal);
  };

  return (
    <>
      <div className="bg-white rounded-lg mb-2 shadow">
        <Collapsible
          open={tableFilterOpen}
          onOpenChange={setTableFilterOpen}
        >
          <div className="flex items-center gap-2 p-2">
            <SearchBar
              placeholder="Title"
              // setCurrentPage={setCurrentPage}
            />
            <CollapsibleTrigger asChild>
              {tableFilterOpen ? (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    handleManageModal();
                    onClearQuery();
                  }}
                >
                  Cancel
                </Button>
              ) : (
                <Button
                  onClick={handleManageModal}
                  size="sm"
                  variant="outline"
                >
                  <ListFilter size={"16px"} />
                </Button>
              )}
            </CollapsibleTrigger>
          </div>
          <CollapsibleContent>
            <OptionSetActionButtons
              shops={shops}
              onShop={onShop}
              onClearQuery={onClearQuery}
            />
          </CollapsibleContent>
        </Collapsible>
      </div>
    </>
  );
}
