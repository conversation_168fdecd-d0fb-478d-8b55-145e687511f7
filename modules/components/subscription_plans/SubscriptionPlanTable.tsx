"use client";

import NoData from "@/components/_components/NoData";
import SearchBar from "@/components/_components/SearchBar";
import TooltipWrapper from "@/components/_components/TooltipWrapper";
import { Button } from "@/components/ui/button";
import { SubscriptionPlanPermissionProvider } from "@/modules/context/SubscriptionPlanPermissionProvider";
import useUserPermission from "@/modules/hooks/useUserPermission";
import { ISubscriptionPlanData } from "@/types/plans";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import AddSubscriptionModal from "./AddSubcscriptionPlanModal";
import SubscriptionPlanTableBody from "./SubscriptionPlanTableBody";

export default function SubscriptionPlanTable({ plans }: { plans: ISubscriptionPlanData[] }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const sortBy = searchParams.get("sortBy");
  const sortOrder = searchParams.get("sortOrder");

  const [sortItem, setSortItem] = useState(sortBy);
  const [orderStatus, setOrderStatus] = useState(sortOrder || "ASC");
  const [addSubscriptionModal, setAddSubscriptionModal] = useState<boolean>(false);

  const { hasWritePermission, hasDeletePermission } = useUserPermission([
    "SUBSCRIPTION_PLAN_READ",
    "SUBSCRIPTION_PLAN_WRITE",
    "SUBSCRIPTION_PLAN_DELETE",
  ]);

  const handleSort = (item: string) => {
    if (item === sortItem) {
      setOrderStatus(orderStatus === "ASC" ? "DESC" : "ASC");
    } else {
      setOrderStatus("ASC");
      setSortItem(item);
    }
  };

  useEffect(() => {
    const searchString = new URLSearchParams(searchParams.toString());

    if (sortItem) {
      searchString.set("sortBy", sortItem);
      searchString.set("sortOrder", orderStatus);
    } else {
      searchString.delete("sortBy");
      searchString.delete("sortOrder");
    }
    router.push(`${pathname}?${searchString.toString()}`);
  }, [orderStatus, pathname, router, searchParams, sortItem]);

  return (
    <>
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold leading-6 text-gray-900">Subscription Plans</h3>

        <TooltipWrapper tooltipText={hasWritePermission || hasDeletePermission ? null : "Insufficient Permission"}>
          <Button
            onClick={hasWritePermission || hasDeletePermission ? () => setAddSubscriptionModal(true) : () => {}}
            size="sm"
            className={hasWritePermission || hasDeletePermission ? "" : "opacity-50 cursor-not-allowed"}
          >
            Add Subscription Plan
          </Button>
        </TooltipWrapper>
      </div>

      <div className="mx-auto w-full my-5">
        <div className="p-2 bg-white rounded-lg mb-2 shadow">
          <SearchBar
            placeholder="Name/Slug"
            setCurrentPage={() => {}}
          />
        </div>

        {plans?.length === 0 ? (
          <NoData />
        ) : (
          <div className="bg-white dark:bg-gray-800 relative shadow sm:rounded-lg overflow-hidden">
            <SubscriptionPlanPermissionProvider>
              <SubscriptionPlanTableBody
                plans={plans}
                onSort={handleSort}
              />
            </SubscriptionPlanPermissionProvider>
          </div>
        )}

        <AddSubscriptionModal
          openModal={addSubscriptionModal}
          setOpenModal={setAddSubscriptionModal}
          title="Add Subscription Plan"
          primaryActionText="Create"
        />
      </div>
    </>
  );
}
