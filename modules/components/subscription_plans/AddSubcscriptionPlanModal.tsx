import DropdownWrapper from "@/components/_components/DropdownWrapper";
import InputField from "@/components/_components/InputField";
import Modal from "@/components/_components/Modal";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DISCOUNT_TYPE,
  discountType,
  features,
  meta,
  PLAN_INTERVALS,
  PLAN_TYPES,
  planInterval,
  planType,
  STATUS,
  status,
} from "@/config/subscription-plans";
import { addPlanAction, updatePlanAction } from "@/modules/_actions/planActions";
import { useCoupons } from "@/modules/context/CouponProvider";
import { AddPlanSchema } from "@/modules/validations/plans";
import { IAddSubscriptionPlan, IPlanMeta, ISubscriptionModal } from "@/types/plans";
import { generateSlug } from "@/utlis/helpers";
import { zodResolver } from "@hookform/resolvers/zod";
import { omit } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

const calculateFinalPrice = (price: number, discount: number, discountType: string) => {
  if (discountType === "percent") {
    return price - price * (discount / 100);
  }
  if (discountType === "amount") {
    return price - discount;
  }
  return price;
};

export default function AddSubscriptionModal({
  title,
  primaryActionText,
  openModal,
  setOpenModal,
  isEditing = false,
  plan,
  isCopied = false,
}: ISubscriptionModal) {
  const coupons = useCoupons();

  const allCoupons = useMemo(() => {
    return coupons?.map((coupon) => ({
      // id: coupon?.id,
      type: coupon?.discount_type,
      amount: coupon?.amount,
      label: coupon?.name,
      value: coupon?.id,
      code: coupon?.code,
    }));
  }, [coupons]);

  const [selectedFeatures, setSelectedFeatures] = useState(features);
  const [formError, setFormError] = useState<null | string>(null);

  const handleFeatureCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target;

    setSelectedFeatures((prevFeatures) =>
      prevFeatures.map((feature) =>
        feature.name === name ? { ...feature, value: feature.value === true ? false : true } : feature
      )
    );
  };

  const {
    control,
    setValue,
    watch,
    reset,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(AddPlanSchema),
    defaultValues: {
      name: "",
      slug: "",
      type: "",
      interval: "",
      price: 0,
      coupon_id: 0,
      discount: 0,
      final_price: 0,
      price_save_description: "",
      discount_type: DISCOUNT_TYPE.PERCENT.value as "amount" | "percent",
      trial_days: 0,
      currency: "USD",
      test: true,
      status: true,
      meta: {} as IPlanMeta,
      package_info: [],
    },
    values: {
      name: isCopied ? `Copy ${plan?.name}` : (plan?.name ?? ""),
      slug: isCopied ? `copy-${plan?.slug}` : (plan?.slug ?? ""),
      type: plan?.type ?? PLAN_TYPES.FREE.value,
      interval: plan?.interval ?? PLAN_INTERVALS.MONTHLY.value,
      price: plan?.price ?? 0,
      coupon_id: plan?.coupon_id,
      discount: plan?.discount ?? 0,
      final_price: plan?.final_price ?? 0,
      price_save_description: plan?.price_save_description,
      trial_days: plan?.trial_days ?? 0,
      currency: "USD",
      discount_type: (plan?.discount_type as "amount" | "percent") ?? undefined,
      test: String(plan?.test ?? STATUS.ACTIVE.value) as unknown as boolean,
      status: String(plan?.status ?? STATUS.ACTIVE.value) as unknown as boolean,
    },
  });

  const selectedCoupon = watch("coupon_id");
  const selectedPrice = watch("price");

  const coupon = useMemo(() => {
    return allCoupons?.find((c) => c.value === Number(selectedCoupon));
  }, [allCoupons, selectedCoupon]);

  const finalPrice = calculateFinalPrice(selectedPrice, coupon?.amount as number, coupon?.type as string);

  const onSubmit = async (data: IAddSubscriptionPlan) => {
    let response;
    const payload = {
      ...data,
      meta: {
        ...meta,
        coupon_code: coupon?.code ? coupon.code : null,
        capped_amount:
          data?.interval === "usage"
            ? { amount: 20, currencyCode: "USD", price_save_description: data.price_save_description ?? "" }
            : null,
      },
      package_info: selectedFeatures,
      final_price: Number(finalPrice?.toFixed(2)),
    };

    const updatedPayload = omit(payload, ["price_save_description"]);

    if (isEditing) {
      response = await updatePlanAction(plan?.id as number, updatedPayload);
    } else if (isCopied) {
      response = await addPlanAction({ id: plan?.id, ...updatedPayload });
    } else {
      response = await addPlanAction(updatedPayload);
    }

    if (response?.success) {
      toast.success(response.message);
      setOpenModal(false);
      reset();
    } else {
      response ? setFormError(response.message) : "";
    }
  };

  useEffect(() => {
    if (!openModal) {
      setSelectedFeatures(features);
      setFormError(null);
      reset();
    }
  }, [openModal, reset, plan]);

  useEffect(() => {
    if (selectedCoupon) {
      setValue("discount", coupon?.amount as number);
      setValue("discount_type", coupon?.type as "percent" | "amount");
    }
  }, [coupon?.amount, coupon?.type, selectedCoupon, setValue]);

  useEffect(() => {
    if (isEditing || isCopied) {
      setSelectedFeatures(plan?.package_info as { value: boolean; display_name: string; name: string }[]);
    }
  }, [plan?.package_info, isEditing, isCopied]);

  return (
    <Modal
      modalTitle={title}
      handlePrimaryAction={handleSubmit(onSubmit)}
      primaryActionText={primaryActionText}
      modalSize="md"
      open={openModal}
      setOpen={(isOpen) => setOpenModal(isOpen)}
      loading={isSubmitting}
    >
      <form>
        {formError && <p className="text-red-500 text-sm my-2 text-center">{formError}</p>}
        <div className="w-full">
          <div className="grid sm:grid-cols-2 gap-4">
            {/* name */}
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Name"
                  placeholder="Enter Name"
                  error={errors?.name?.message}
                  {...field}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    field.onChange(newValue);
                    setValue("slug", generateSlug(newValue));
                  }}
                />
              )}
            />

            {/* slug */}
            <Controller
              name="slug"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Slug"
                  placeholder="Enter Slug"
                  error={errors?.slug?.message}
                  {...field}
                />
              )}
            />

            {/* Plan Type */}
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <DropdownWrapper
                  label="Plan Type"
                  items={planType}
                  title="Enter Subscription Type"
                  error={errors?.type?.message}
                  field={field}
                />
              )}
            />

            {/* PLan Interval */}
            <Controller
              name="interval"
              control={control}
              render={({ field }) => {
                return (
                  <DropdownWrapper
                    label="Plan Interval"
                    items={planInterval}
                    title="Enter Plan Interval"
                    error={errors?.interval?.message}
                    field={field}
                  />
                );
              }}
            />

            {/* Price */}
            <Controller
              name="price"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Price"
                  type="number"
                  placeholder="Enter Price"
                  error={errors?.price?.message}
                  {...field}
                  value={field.value?.toString()}
                  onChange={(e) => {
                    const newValue = Number(e.target.value);
                    field.onChange(newValue);
                    setValue("final_price", newValue);
                  }}
                />
              )}
            />

            {/* Coupons */}
            <Controller
              name="coupon_id"
              control={control}
              render={({ field }) => (
                <DropdownWrapper
                  label="Coupons"
                  required={false}
                  error={errors?.coupon_id?.message}
                  items={allCoupons}
                  title="Enter Coupon"
                  field={field}
                />
              )}
            />

            {/* Discount */}
            <Controller
              name="discount"
              control={control}
              render={({ field }) => {
                return (
                  <InputField
                    label="Discount"
                    disabled
                    type="number"
                    placeholder="Discount"
                    error={errors?.discount?.message}
                    {...field}
                    value={field.value?.toString()}
                  />
                );
              }}
            />

            {/* Discount Type */}
            <Controller
              name="discount_type"
              control={control}
              render={({ field }) => {
                return (
                  <DropdownWrapper
                    label="Discount Type"
                    disabled
                    items={discountType}
                    required={false}
                    title="Enter Discount Type"
                    field={field}
                  />
                );
              }}
            />

            {/* Final Price */}
            <Controller
              name="final_price"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Final Price"
                  disabled
                  required
                  type="number"
                  placeholder="Final Price"
                  error={errors?.final_price?.message}
                  {...field}
                  value={finalPrice?.toFixed(2)}
                />
              )}
            />

            {/* Price Save Description */}
            <Controller
              name="price_save_description"
              control={control}
              render={({ field }) => {
                return (
                  <InputField
                    required={false}
                    label="Price Save Description"
                    placeholder="Enter Price Save Description"
                    error={errors?.price_save_description?.message}
                    {...field}
                  />
                );
              }}
            />

            {/* Trial Days */}
            <Controller
              name="trial_days"
              control={control}
              render={({ field }) => {
                return (
                  <InputField
                    label="Trial Days"
                    type="number"
                    placeholder="Enter Trial Days"
                    error={errors?.trial_days?.message}
                    {...field}
                    value={field.value?.toString()}
                  />
                );
              }}
            />

            {/* Currency */}
            <Controller
              name="currency"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Currency"
                  disabled
                  error={errors?.currency?.message}
                  value="USD"
                />
              )}
            />

            {/* Test Charge */}
            <Controller
              name="test"
              control={control}
              render={({ field }) => {
                return (
                  <DropdownWrapper
                    label="Test Charge"
                    items={status}
                    title="Enter Test Charge"
                    error={errors?.test?.message}
                    field={field}
                  />
                );
              }}
            />

            {/* Status */}
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <DropdownWrapper
                  label="Status"
                  items={status}
                  title="Status"
                  error={errors?.status?.message}
                  field={field}
                />
              )}
            />
          </div>

          {/* Package Info */}
          <div className="mt-2">
            <Label className="text-[13px] text-gray-500 font-normal block mb-1">
              Package Info <span className="text-red-500"> *</span>
            </Label>

            <div className="flex gap-2 flex-wrap">
              {selectedFeatures?.map((feature, index) => (
                <div key={index}>
                  <label>
                    <Input
                      type="checkbox"
                      name={feature.name}
                      className="hidden"
                      onChange={handleFeatureCheck}
                      checked={feature.value}
                    />
                    <Badge
                      variant={feature?.value ? "success" : "default"}
                      className="cursor-pointer"
                    >
                      {feature.display_name}
                    </Badge>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
}
