import SortableColumn from "@/components/_components/SortableColumn";
import { deletePlanAction, getPlanAction } from "@/modules/_actions/planActions";
import { useConfirm } from "@/modules/hooks/useConfirm";
import { ISubscriptionPlanData } from "@/types/plans";
import { useState } from "react";
import toast from "react-hot-toast";
import AddSubscriptionModal from "./AddSubcscriptionPlanModal";
import SubscriptionPlanTableData from "./SubscriptionPlanTableData";

export default function SubscriptionPlanTableBody({
  plans,
  onSort,
}: {
  plans: ISubscriptionPlanData[];
  onSort: (name: string) => void;
}) {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [planData, setPlanData] = useState<ISubscriptionPlanData | null>(null);
  const [id, setId] = useState<number | null>(null);

  const { ConfirmAlert, showConfirm } = useConfirm();

  const handleEditModal = async (id: number) => {
    setIsEditing(true);
    const response = await getPlanAction(id);
    if (response.success) {
      setPlanData(response?.data);
    }
  };

  const handleCopyModal = async (id: number) => {
    setIsCopied(true);
    const response = await getPlanAction(id);
    if (response.success) {
      setPlanData(response?.data);
    }
  };

  const handleDelete = async () => {
    const response = await deletePlanAction(id);

    if (response.success) {
      toast.success(response.message);
    } else {
      toast.error(response?.message as string);
    }
  };

  const tableHeaders = [
    { key: "index", heading: "#", isSortable: false },
    { key: "name", heading: "Name", isSortable: true, extraClass: "cursor-pointer" },
    { key: "slug", heading: "Slug", isSortable: false },
    { key: "type", heading: "Type", isSortable: true, extraClass: "cursor-pointer" },
    { key: "interval", heading: "Interval", isSortable: false },
    { key: "price", heading: "Price", isSortable: true, extraClass: "cursor-pointer" },
    { key: "status", heading: "Status", isSortable: false },
    { key: "created_at", heading: "Created At", isSortable: true, extraClass: "cursor-pointer" },
    { key: "actions", heading: "Actions", isSortable: false, extraClass: "text-center w-28" },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        {/* Table Head */}
        <thead className="text-xs text-gray-700 uppercase border-b-2 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-400">
          <tr>
            {tableHeaders.map((header) => (
              <th
                key={header.key}
                scope="col"
                className={`px-2 py-2 ${header?.extraClass}`}
              >
                {header.isSortable ? (
                  <SortableColumn
                    heading={header.heading}
                    name={header.key}
                    onSort={onSort}
                  />
                ) : (
                  header.heading
                )}
              </th>
            ))}
          </tr>
        </thead>

        {/* Table Data */}
        <tbody>
          <>
            {plans?.map((item, idx) => (
              <SubscriptionPlanTableData
                key={item?.id}
                index={idx}
                currentPage={1}
                limit={20}
                item={item}
                onEditModal={handleEditModal}
                onCopyModal={handleCopyModal}
                onDelete={() => {
                  showConfirm();
                  setId(item?.id);
                }}
              />
            ))}
          </>
        </tbody>
      </table>

      <ConfirmAlert
        title="Are you sure?"
        content="You want to delete this admin? The action cannot be undone."
        confirmAction={{
          onAction: handleDelete,
          label: "Yes, Delete",
          variant: "danger",
          loading: false,
        }}
      />

      {isEditing && (
        <AddSubscriptionModal
          openModal={isEditing}
          setOpenModal={setIsEditing}
          isEditing={isEditing}
          plan={planData as ISubscriptionPlanData}
          title={"Edit Subscription Plan"}
          primaryActionText={"Update"}
        />
      )}

      {isCopied && (
        <AddSubscriptionModal
          openModal={isCopied}
          setOpenModal={setIsCopied}
          isCopied={isCopied}
          plan={planData as ISubscriptionPlanData}
          title={"Copy Subscription Plan"}
          primaryActionText={"Save"}
        />
      )}
    </div>
  );
}
