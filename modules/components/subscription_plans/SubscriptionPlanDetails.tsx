import DescriptionList from "@/components/_components/DescriptionList";
import { getPlan } from "@/lib/plan.lib";
import { subcriptionPlanDetailsFormatter } from "@/modules/mappers/subscription_plan/subscriptionPlanMapper";

export default async function SubscriptionPlanDetails({ id }: { id: number }) {
  const plan = await getPlan(id);

  const planDetails = subcriptionPlanDetailsFormatter(plan);

  return <DescriptionList listData={planDetails} />;
}
