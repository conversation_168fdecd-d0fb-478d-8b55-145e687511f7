import TableIconActions, { TableIconAction } from "@/components/_components/TableIconActions";
import { Badge } from "@/components/ui/badge";
import { ISubscriptionPlanData } from "@/types/plans";
import { calculateDynamicId, formattedDate } from "@/utlis/helpers";
import { FilePenLine, Files, InfoIcon, Trash2 } from "lucide-react";

export default function SubscriptionPlanTableData({
  index,
  currentPage = 1,
  limit = 20,
  item,
  onEditModal,
  onCopyModal,
  onDelete,
}: {
  index: number;
  currentPage: number;
  limit: number;
  item: ISubscriptionPlanData;
  onEditModal: (id: number) => void;
  onCopyModal: (id: number) => void;
  onDelete: () => void;
}) {
  // const { hasWritePermission, hasDeletePermission } = useSubscriptionPlanPermissions();
  const { hasWritePermission, hasDeletePermission } = { hasWritePermission: false, hasDeletePermission: false };

  const actions: TableIconAction[] = [
    {
      title: "See Details",
      Icon: InfoIcon,
      url: `/settings/subscription-plans/${item?.id}`,
      variant: "default",
    },
    {
      title: true ? "Edit Plan" : "Insufficient Permission",
      Icon: FilePenLine,
      onAction: true ? () => onEditModal(item?.id) : () => {},
      variant: true ? "info" : "disabled",
    },
    {
      title: hasWritePermission || hasDeletePermission ? "Copy Plan" : "Insufficient Permission",
      Icon: Files,
      onAction: hasWritePermission || hasDeletePermission ? () => onCopyModal(item?.id) : () => {},
      variant: hasWritePermission || hasDeletePermission ? "warning" : "disabled",
    },
    {
      title: hasDeletePermission ? "Delete Plan" : "Insufficient Permission",
      Icon: Trash2,
      onAction: hasDeletePermission ? onDelete : () => {},
      variant: hasDeletePermission ? "danger" : "disabled",
    },
  ];

  return (
    <>
      <tr className="border-b dark:border-gray-700 group text-sm last:border-b-0">
        <td className="px-2 py-1">{calculateDynamicId(index, currentPage, limit)}</td>
        <td className="px-2 py-1">{item?.name}</td>
        <td className="px-2 py-1">{item?.slug}</td>
        <td className="px-2 py-1">
          <Badge
            size="xsm"
            variant={item?.type === "pro" ? "info" : "default"}
            textTransform="capital"
          >
            {item?.type}
          </Badge>
        </td>

        <td className="px-2 py-1 capitalize">{item?.interval}</td>

        <td className="px-2 py-1">
          {item?.coupon_id ? (
            <>
              <p className="line-through text-xs">${item?.price}</p> ${item.final_price}
            </>
          ) : (
            <span>${item?.final_price}</span>
          )}
        </td>

        <td className="px-2 py-1">
          <Badge
            size="xsm"
            variant={item?.status === true ? "success" : "danger"}
            textTransform="capital"
          >
            {item?.status ? "active" : "inactive"}
          </Badge>
        </td>

        <td className="px-2 py-1 ml-3">{formattedDate(item?.created_at)}</td>

        {/* Action Button */}

        <td className="px-2 py-1">
          <TableIconActions actions={actions} />
        </td>
      </tr>
    </>
  );
}
