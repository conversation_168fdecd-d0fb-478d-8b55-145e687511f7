"use client";

import NoData from "@/components/_components/NoData";
import Pagination from "@/components/_components/Pagination";
import SearchBar from "@/components/_components/SearchBar";
import TooltipWrapper from "@/components/_components/TooltipWrapper";
import { But<PERSON> } from "@/components/ui/button";
import { CouponPermissionProvider } from "@/modules/context/CouponPermissonProvider";
import useUserPermission from "@/modules/hooks/useUserPermission";
import { ICoupon } from "@/types/coupons";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import AddCouponModal from "./AddCouponModal";
import CouponTableBody from "./CouponTableBody";

export default function CouponTable({ data }: { data: ICoupon }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const pageNumber = Number(searchParams.get("page"));
  const pageLimit = Number(searchParams.get("limit"));
  const sortBy = searchParams.get("sortBy");
  const sortOrder = searchParams.get("sortOrder");

  const [currentPage, setCurrentPage] = useState<number>(pageNumber || 1);
  const [limit, setLimit] = useState<number>(pageLimit || 20);
  const [sortItem, setSortItem] = useState(sortBy);
  const [orderStatus, setOrderStatus] = useState(sortOrder || "ASC");
  const [addCouponModal, setAddCouponModal] = useState<boolean>(false);

  const { hasWritePermission, hasDeletePermission } = useUserPermission([
    "COUPON_READ",
    "COUPON_WRITE",
    "COUPON_DELETE",
  ]);

  const handleSort = (item: string) => {
    if (item === sortItem) {
      setOrderStatus(orderStatus === "ASC" ? "DESC" : "ASC");
    } else {
      setOrderStatus("ASC");
      setSortItem(item);
    }
  };

  useEffect(() => {
    const searchString = new URLSearchParams(searchParams.toString());

    if (sortItem) {
      searchString.set("sortBy", sortItem);
      searchString.set("sortOrder", orderStatus);
    } else {
      searchString.delete("sortBy");
      searchString.delete("sortOrder");
    }
    router.push(`${pathname}?${searchString.toString()}`);
  }, [orderStatus, pathname, router, searchParams, sortItem]);

  return (
    <>
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold leading-6 text-gray-900">Coupons</h3>

        <TooltipWrapper tooltipText={hasWritePermission || hasDeletePermission ? null : "Insufficient Permission"}>
          <Button
            onClick={hasWritePermission || hasDeletePermission ? () => setAddCouponModal(true) : () => {}}
            size="sm"
            className={hasWritePermission || hasDeletePermission ? "" : "opacity-50 cursor-not-allowed"}
          >
            Add Coupon
          </Button>
        </TooltipWrapper>
      </div>

      <div className="mx-auto w-full my-5">
        <div className="p-2 bg-white rounded-lg mb-2 shadow">
          <SearchBar
            placeholder="Name/Coupon"
            setCurrentPage={() => {}}
          />
        </div>

        {data?.count === 0 ? (
          <NoData />
        ) : (
          <div className="bg-white dark:bg-gray-800 relative shadow sm:rounded-lg overflow-hidden">
            <CouponPermissionProvider>
              <CouponTableBody
                coupons={data?.coupons}
                onSort={handleSort}
                currentPage={currentPage}
                limit={limit}
              />
            </CouponPermissionProvider>
            <Pagination
              count={data?.count}
              shopCount={data?.count}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              limit={limit}
            />
          </div>
        )}
      </div>

      <AddCouponModal
        openModal={addCouponModal}
        setOpenModal={setAddCouponModal}
        title="Add Coupon"
        primaryActionText="Create"
      />
    </>
  );
}
