import DropdownWrapper from "@/components/_components/DropdownWrapper";
import FilterDateRange from "@/components/_components/FilterDateRange";
import InputField from "@/components/_components/InputField";
import Modal from "@/components/_components/Modal";
import { MultiSelect } from "@/components/_components/MultiSelect";
import { Label } from "@/components/ui/label";
import { DISCOUNT_TYPE, discountType, STATUS, status } from "@/config/subscription-plans";
import { addCouponAction, updateCouponAction } from "@/modules/_actions/couponActions";
import { useSubscriptionPlans } from "@/modules/context/SubscriptionPlanProvider";
import { AddCouponSchema } from "@/modules/validations/coupons";
import { IAddCoupon, ICouponModalProps, IUpdateCoupon } from "@/types/coupons";
import { zodResolver } from "@hookform/resolvers/zod";
import { omit } from "lodash";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

const AddCouponModal = ({
  openModal,
  setOpenModal,
  isEditing = false,
  isCopied = false,
  title,
  coupon,
  primaryActionText,
}: ICouponModalProps) => {
  const [plans, setPlans] = useState<string[]>([]);
  const [formError, setFormError] = useState<string | null>(null);

  const allPlans = useSubscriptionPlans();

  const newPlans = allPlans?.map((item) => {
    const newObject = {
      value: item?.slug,
      label: `${item?.name} (${item?.interval})`,
    };
    return newObject;
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(AddCouponSchema),
    defaultValues: {
      name: "",
      code: "",
      amount: 0,
      discount_type: DISCOUNT_TYPE.PERCENT.value as "amount" | "percent",
      discount_limit_duration: 0,
      start_date: new Date(),
      end_date: new Date(),
      max_limit: 0,
      plans: [],
      status: true,
    },
    values: {
      name: isCopied ? `Copy ${coupon?.name}` : (coupon?.name ?? ""),
      code: isCopied ? `Copy ${coupon?.code}` : (coupon?.code ?? ""),
      amount: coupon?.amount ?? 0,
      discount_type: (coupon?.discount_type as "amount" | "percent") ?? DISCOUNT_TYPE.PERCENT.value,
      discount_limit_duration: coupon?.discount_limit_duration ?? 0,
      max_limit: coupon?.max_limit ?? 0,
      start_date: coupon?.start_date as unknown as Date,
      end_date: coupon?.end_date as unknown as Date,
      plans: coupon?.plans as unknown as string[],
      status: String(coupon?.status ?? STATUS.ACTIVE.value) as unknown as boolean,
    },
  });

  const onSubmit = async (data: IAddCoupon) => {
    let response;

    const payload = {
      ...data,
      start_date: new Date(data.start_date).toDateString(),
      end_date: new Date(data.end_date).toDateString(),
      plans: plans,
    };

    if (isEditing) {
      const updatePayload = omit(payload, ["code", "amount", "discount_type"]) as unknown as IUpdateCoupon;
      response = await updateCouponAction(coupon?.id as number, updatePayload);
    } else if (isCopied) {
      response = await addCouponAction({ id: coupon?.id, ...payload });
    } else {
      response = await addCouponAction(payload);
    }

    if (response?.success) {
      toast.success(response.message);
      setOpenModal(false);
      reset();
    } else {
      response ? setFormError(response.message) : "";
    }
  };

  useEffect(() => {
    if (!openModal) {
      reset();
      setPlans([]);
      setFormError(null);
    }
  }, [openModal, reset]);

  useEffect(() => {
    if (coupon?.plans) {
      setPlans(coupon?.plans);
    }
  }, [coupon?.plans]);

  return (
    <Modal
      modalTitle={title}
      modalSize="md"
      handlePrimaryAction={handleSubmit(onSubmit)}
      primaryActionText={primaryActionText}
      open={openModal}
      setOpen={setOpenModal}
      loading={isSubmitting}
    >
      <form>
        {formError && <p className="text-red-500 text-sm my-2 text-center">{formError}</p>}

        <div className="grid sm:grid-cols-2 gap-4">
          {/* Name */}
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <InputField
                label="Name"
                placeholder="Enter Coupon Name"
                error={errors?.name?.message}
                {...field}
              />
            )}
          />

          {/* Code */}
          <Controller
            name="code"
            control={control}
            render={({ field }) => (
              <InputField
                label="Code"
                disabled={isEditing}
                placeholder="Enter Coupon Code"
                error={errors?.code?.message}
                {...field}
              />
            )}
          />

          {/* Discount Amount */}
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <InputField
                label="Discount Amount"
                disabled={isEditing}
                type="number"
                placeholder="Discount Amount"
                error={errors?.amount?.message}
                {...field}
                value={field.value?.toString()}
              />
            )}
          />

          {/* Discount Type */}
          <Controller
            name="discount_type"
            control={control}
            render={({ field }) => (
              <DropdownWrapper
                label="Discount Type"
                items={discountType}
                field={field}
                title="Discount Type"
                disabled={isEditing}
                error={errors?.discount_type?.message}
              />
            )}
          />

          {/* Discount Duration */}
          <Controller
            name="discount_limit_duration"
            control={control}
            render={({ field }) => (
              <InputField
                label="Discount Duration"
                required={false}
                type="number"
                placeholder="Discount Duration"
                {...field}
                value={field.value?.toString()}
              />
            )}
          />

          {/* Max Limit */}
          <Controller
            name="max_limit"
            control={control}
            render={({ field }) => (
              <InputField
                label="Max Limit"
                type="number"
                placeholder="No. of max redemption"
                error={errors?.max_limit?.message}
                {...field}
                value={field.value?.toString()}
              />
            )}
          />

          {/* Start Date */}
          <Controller
            name="start_date"
            control={control}
            render={({ field }) => (
              <FilterDateRange
                label="Start Date"
                value={field.value && new Date(field.value)}
                error={errors.start_date?.message}
                onChange={(date: Date) => field.onChange(new Date(date) ? date : "")}
              />
            )}
          />

          {/* End Date */}
          <Controller
            name="end_date"
            control={control}
            render={({ field }) => (
              <FilterDateRange
                label="End Date"
                value={field.value && new Date(field.value)}
                error={errors.end_date?.message}
                onChange={(date: Date) => field.onChange(new Date(date) ? date : "")}
              />
            )}
          />
          {/* Plans */}
          <div>
            <Label className="text-[13px] text-gray-500 font-normal mb-1 inline-block">
              Plans<span className="text-red-500"> *</span>
            </Label>
            <MultiSelect
              options={newPlans}
              onValueChange={setPlans}
              defaultValue={plans as string[]}
              placeholder="Select Plans"
            />
          </div>

          {/* Status */}
          <Controller
            name="status"
            control={control}
            render={({ field }) => (
              <DropdownWrapper
                label="Status"
                items={status}
                title="Status"
                error={errors?.status?.message}
                field={field}
              />
            )}
          />
        </div>
      </form>
    </Modal>
  );
};

export default AddCouponModal;
