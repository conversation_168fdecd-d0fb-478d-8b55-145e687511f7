import SortableColumn from "@/components/_components/SortableColumn";
import { deleteCouponAction, getCouponAction } from "@/modules/_actions/couponActions";
import { useConfirm } from "@/modules/hooks/useConfirm";
import { ICouponData } from "@/types/coupons";
import { useState } from "react";
import toast from "react-hot-toast";
import AddCouponModal from "./AddCouponModal";
import CouponTableData from "./CouponTableData";

export default function CouponTableBody({
  coupons,
  onSort,
  currentPage,
  limit,
}: {
  coupons: ICouponData[];
  onSort: (value: string) => void;
  currentPage: number;
  limit: number;
}) {
  const { ConfirmAlert, showConfirm } = useConfirm();

  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [id, setId] = useState<number | null>(null);
  const [couponData, setCouponData] = useState<ICouponData | null>(null);

  const handleEditModal = async (id: number) => {
    setIsEditing(true);
    const response = await getCouponAction(id);
    if (response.success) {
      setCouponData(response?.data);
    }
  };

  const handleCopyModal = async (id: number) => {
    setIsCopied(true);
    const response = await getCouponAction(id);
    if (response.success) {
      setCouponData(response?.data);
    }
  };

  const handleDelete = async () => {
    const response = await deleteCouponAction(id);

    if (response.success) {
      toast.success(response.message);
    } else {
      toast.error(response?.message as string);
    }
  };

  // Define table headers as an array
  const tableHeaders = [
    { key: "index", heading: "#", isSortable: false },
    { key: "name", heading: "Name", isSortable: true, extraClass: "cursor-pointer" },
    { key: "code", heading: "Code", isSortable: false },
    { key: "amount", heading: "Amount", isSortable: true, extraClass: "cursor-pointer" },
    { key: "duration", heading: "Duration", isSortable: false },
    { key: "dateRange", heading: "Date Range", isSortable: false },
    { key: "status", heading: "Status", isSortable: false },
    { key: "created_at", heading: "Created At", isSortable: true, extraClass: "cursor-pointer" },
    { key: "actions", heading: "Actions", isSortable: false, extraClass: "text-center w-28" },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        {/* Table Head */}
        <thead className="text-xs text-gray-700 uppercase border-b-2 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-400">
          <tr>
            {tableHeaders.map((header) => (
              <th
                key={header.key}
                scope="col"
                className={`px-2 py-2 ${header?.extraClass}`}
              >
                {header.isSortable ? (
                  <SortableColumn
                    heading={header.heading}
                    name={header.key}
                    onSort={onSort}
                  />
                ) : (
                  header.heading
                )}
              </th>
            ))}
          </tr>
        </thead>

        {/* Table Data */}
        <tbody>
          {coupons?.map((coupon, idx) => (
            <CouponTableData
              key={coupon?.id}
              index={idx}
              currentPage={currentPage}
              limit={limit}
              item={coupon}
              onEditModal={handleEditModal}
              onCopyModal={handleCopyModal}
              onDelete={() => {
                showConfirm();
                setId(coupon?.id);
              }}
            />
          ))}
        </tbody>
      </table>

      <ConfirmAlert
        title="Are you sure?"
        content="You want to delete this admin? The action cannot be undone."
        confirmAction={{
          onAction: handleDelete,
          label: "Yes, Delete",
          variant: "danger",
          loading: false,
        }}
      />

      {isEditing && (
        <AddCouponModal
          openModal={isEditing}
          setOpenModal={setIsEditing}
          isEditing={isEditing}
          title="Edit Coupon"
          coupon={couponData as ICouponData}
          primaryActionText="Update"
        />
      )}

      {isCopied && (
        <AddCouponModal
          openModal={isCopied}
          setOpenModal={setIsCopied}
          isCopied={isCopied}
          title="Copy Coupon"
          coupon={couponData as ICouponData}
          primaryActionText="Save"
        />
      )}
    </div>
  );
}
