import DescriptionList from "@/components/_components/DescriptionList";
import { getCoupon } from "@/lib/coupon.lib";
import { couponDetailsFormatter } from "@/modules/mappers/coupon/couponMapper";

export default async function CouponDetails({ id }: { id: number }) {
  const coupon = await getCoupon(id);

  const couponDetails = couponDetailsFormatter(coupon);

  return <DescriptionList listData={couponDetails} />;
}
