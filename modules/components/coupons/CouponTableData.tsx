import TableIconActions, { TableIconAction } from "@/components/_components/TableIconActions";
import { Badge } from "@/components/ui/badge";
import { useCouponPermissions } from "@/modules/hooks/useCouponPermission";
import { ICouponData } from "@/types/coupons";
import { calculateDynamicId, formattedDate } from "@/utlis/helpers";
import { Copy, FilePenLine, Files, InfoIcon, Trash2 } from "lucide-react";
import { useRef } from "react";
import toast from "react-hot-toast";

const notify = () => toast.success("Copied");

export default function CouponTableData({
  item,
  index,
  currentPage,
  limit,
  onEditModal,
  onCopyModal,
  onDelete,
}: {
  item: ICouponData;
  index: number;
  currentPage: number;
  limit: number;
  onEditModal: (id: number) => void;
  onCopyModal: (id: number) => void;
  onDelete: () => void;
}) {
  const couponRef = useRef(null);

  const { hasWritePermission, hasDeletePermission } = useCouponPermissions();

  const actions: TableIconAction[] = [
    {
      title: "See Details",
      Icon: InfoIcon,
      url: `/settings/coupons/${item?.id}`,
      variant: "default",
    },

    {
      title: hasWritePermission || hasDeletePermission ? "Edit Coupon" : "Insufficient Permission",
      Icon: FilePenLine,
      onAction: hasWritePermission || hasDeletePermission ? () => onEditModal(item?.id) : () => {},
      variant: hasWritePermission || hasDeletePermission ? "info" : "disabled",
    },
    {
      title: hasWritePermission || hasDeletePermission ? "Copy Coupon" : "Insufficient Permission",
      Icon: Files,
      onAction: hasWritePermission || hasDeletePermission ? () => onCopyModal(item?.id) : () => {},
      variant: hasWritePermission || hasDeletePermission ? "warning" : "disabled",
    },
    {
      title: hasDeletePermission ? "Delete Coupon" : "Insufficient Permission",
      Icon: Trash2,
      onAction: hasDeletePermission ? onDelete : () => {},
      variant: hasDeletePermission ? "danger" : "disabled",
    },
  ];

  const copyCoupon = () => {
    window.navigator.clipboard.writeText(item?.code);
  };

  let startDate, endDate;
  if (item?.start_date) {
    startDate = formattedDate(item?.start_date);
  }

  if (item?.end_date) {
    endDate = formattedDate(item?.end_date);
  }

  let dateRange = "";
  if (startDate && endDate) {
    dateRange = `${startDate} -${endDate}`;
  } else if (startDate && !endDate) {
    dateRange = `${startDate}`;
  } else if (endDate && !startDate) {
    dateRange = ` - ${endDate}`;
  }

  return (
    <>
      <tr className="border-b dark:border-gray-700 group text-sm last:border-b-0">
        <td className="px-2 py-1">{calculateDynamicId(index, currentPage, limit)}</td>
        <td className="px-2 py-1">{item?.name}</td>
        <td
          className="px-2 py-2"
          ref={couponRef}
          onClick={copyCoupon}
        >
          <div className="flex items-center gap-3">
            <Badge
              className="cursor-pointer"
              variant="info"
              onClick={notify}
            >
              {item?.code}
            </Badge>
            <Copy
              size="14px"
              onClick={notify}
              className="cursor-pointer invisible group-hover:visible"
            />
          </div>
        </td>
        <td className="px-2 py-2">
          {item?.discount_type === "percent" ? (
            <div>
              {item?.amount}
              <span>%</span>
            </div>
          ) : (
            <div>
              <span>$</span>
              {item?.amount}
            </div>
          )}
        </td>
        <td className="px-2 py-2">
          {item?.discount_limit_duration ? (
            <div className="flex gap-2">
              {item?.discount_limit_duration}
              <span className="text-xsm">Billing Cycles</span>
            </div>
          ) : (
            "-"
          )}
        </td>
        <td className="px-2 py-1 capitalize">{dateRange}</td>

        <td className="px-2 py-1">
          <Badge
            size="xsm"
            variant={item?.status === true ? "success" : "danger"}
            textTransform="capital"
          >
            {item?.status ? "active" : "inactive"}
          </Badge>
        </td>

        <td className="px-2 py-1 ml-3">{formattedDate(item?.created_at)}</td>

        {/* Action Button */}

        <td className="px-2 py-1">
          <TableIconActions actions={actions} />
        </td>
      </tr>
    </>
  );
}
