import datastore from "@/lib/datastore";
import StoresTable from "./StoresTable";

type IStoresParams = {
  search?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: string;
  page?: string;
  limit?: string;
  plan?: string;
};

export default async function StoresList({ searchParams }: { searchParams?: IStoresParams }) {
  const { search, status, startDate, endDate, sortBy, sortOrder, page, limit, plan } = searchParams || {};

  // const shops = await cache(
  //   () => datastore.shop.getShops(search, status, startDate, endDate, sortBy, sortOrder, page, limit),
  //   [search, status, startDate, endDate, sortBy, sortOrder, page, limit] as string[],
  //   {
  //     revalidate: 3600,
  //     tags: ["stores"],
  //   }
  // )();

  const shops = await datastore.shop.getShops(search, status, startDate, endDate, sortBy, sortOrder, page, limit, plan);

  // Fetch option stats for each store
  const storesWithStats = await Promise.all(
    shops.stores.map(async (store: any) => {
      try {
        const [optionStats, optionSetStats] = await Promise.all([
          datastore.option.getOptionStatsByDomain(store.domain || ''),
          datastore.option.getOptionSetStatsByDomain(store.domain || ''),
        ]);

        return {
          ...store,
          optionStats,
          optionSetStats,
        };
      } catch (error) {
        console.error(`Error fetching stats for ${store.domain}:`, error);
        return {
          ...store,
          optionStats: { total: 0, withPrice: 0, withoutPrice: 0 },
          optionSetStats: { total: 0 },
        };
      }
    })
  );

  const storesWithStatsData = {
    ...shops,
    stores: storesWithStats,
  };

  return <StoresTable stores={JSON.parse(JSON.stringify(storesWithStatsData))} />;
}
