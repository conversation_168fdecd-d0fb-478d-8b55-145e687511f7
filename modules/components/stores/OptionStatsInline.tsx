import { IOptionStats } from "@/types/optionSet";

interface OptionStatsInlineProps {
  stats: IOptionStats;
}

export default function OptionStatsInline({ stats }: OptionStatsInlineProps) {

  if (stats.total === 0) {
    return (
      <div className="text-xs text-gray-500">
        <div>Total: 0</div>
      </div>
    );
  }

  return (
    <div className="text-xs space-y-1">
      <div className="font-medium text-gray-900">
        Total: {stats.total}
      </div>
      <div className="text-gray-600">
        With Price: {stats.withPrice}
      </div>
      <div className="text-gray-600">
        Without Price: {stats.withoutPrice}
      </div>
    </div>
  );
}
