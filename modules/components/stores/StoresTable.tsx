"use client";

import NoData from "@/components/_components/NoData";
import Pagination from "@/components/_components/Pagination";
import { IStores } from "@/types/stores";
import { isEmpty } from "lodash";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import StoreSearchFilter from "./StoreSearchFilter";
import StoresTableBody from "./StoresTableBody";

export default function StoresTable({ stores }: { stores: IStores }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const pageNumber = Number(searchParams.get("page"));
  const pageLimit = Number(searchParams.get("limit"));
  const sortBy = searchParams.get("sortBy");
  const sortOrder = searchParams.get("sortOrder");

  const [currentPage, setCurrentPage] = useState<number>(pageNumber || 1);
  const [limit, setLimit] = useState<number>(pageLimit || 20);
  const [storeStatus, setStoreStatus] = useState<string | null>(null);
  const [planType, setPlanType] = useState<string | null>(null);
  const [sortItem, setSortItem] = useState(sortBy);
  const [orderStatus, setOrderStatus] = useState(sortOrder || "ASC");

  const handleStatus = (status: string) => {
    setStoreStatus(status);
    setCurrentPage(1);
  };

  const handlePlan = (plan: string) => {
    setPlanType(plan);
    setCurrentPage(1);
  };

  const handleSort = (item: string) => {
    if (item === sortItem) {
      setOrderStatus(orderStatus === "ASC" ? "DESC" : "ASC");
    } else {
      setOrderStatus("ASC");
      setSortItem(item);
    }
  };

  const handleClearQuery = () => {
    setCurrentPage(1);
    router.replace("/stores");
    // setSearchTerm("");
    setLimit(20);
    setStoreStatus(null);
    setPlanType(null);
    setSortItem(null);
    // setDateQuery(false);
  };

  const debouncedSearchParams = useDebouncedCallback(({ storeStatus, planType }) => {
    const currentSearchParams = new URLSearchParams(searchParams.toString());

    if (storeStatus) {
      currentSearchParams.set("status", storeStatus);
    } else {
      currentSearchParams.delete("status");
    }

    if (planType) {
      currentSearchParams.set("plan", planType);
    } else {
      currentSearchParams.delete("plan");
    }

    router.push(`${pathname}?${currentSearchParams.toString()}`);
  }, 1000);

  useEffect(() => {
    const searchString = new URLSearchParams(searchParams.toString());

    if (currentPage !== 1) {
      searchString.set("page", currentPage.toString());
    } else {
      searchString.delete("page");
    }

    if (sortItem) {
      searchString.set("sortBy", sortItem);
      searchString.set("sortOrder", orderStatus);
    } else {
      searchString.delete("sortBy");
      searchString.delete("sortOrder");
    }

    // if (limit === 20) {
    //   searchString.delete("limit");
    // }

    // if (!dateQuery) {
    //   searchString.delete("startDate");
    //   searchString.delete("endDate");
    //   setDateQuery(true);
    // }

    router.push(`${pathname}?${searchString.toString()}`);
  }, [pathname, router, searchParams, sortItem, orderStatus, currentPage, limit]);

  useEffect(() => {
    debouncedSearchParams({ storeStatus, planType });
  }, [storeStatus, planType, debouncedSearchParams]);

  return (
    <>
      <h2 className="text-base font-semibold leading-6 text-gray-900 mt-6">List of Stores ({stores?.totalCount})</h2>
      <section className="my-4 w-full mt-2">
        <StoreSearchFilter
          onClearQuery={handleClearQuery}
          onStatus={handleStatus}
          onPlan={handlePlan}
        />

        <div className="bg-white shadow sm:rounded-lg overflow-hidden">
          {isEmpty(stores?.stores) ? (
            <NoData />
          ) : (
            <>
              <StoresTableBody
                stores={stores}
                onSort={handleSort}
                currentPage={currentPage}
                limit={limit}
              />
              <Pagination
                count={stores?.totalCount}
                shopCount={stores?.shopsCount}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                limit={limit}
              />
            </>
          )}
        </div>
      </section>
    </>
  );
}
