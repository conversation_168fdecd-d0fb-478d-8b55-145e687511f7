import SortableColumn from "@/components/_components/SortableColumn";
import { IShopData, IStores } from "@/types/stores";
import StoresTableData from "./StoresTableData";

export default function StoresTableBody({
  stores,
  onSort,
  currentPage,
  limit,
}: {
  stores: IStores;
  onSort: (value: string) => void;
  currentPage: number;
  limit: number;
}) {
  const tableHeaders = [
    { key: "index", heading: "#", isSortable: false },
    { key: "name", heading: "Store", isSortable: true, extraClass: "cursor-pointer" },
    { key: "subscription_plan", heading: "Subscription Plan", isSortable: false },
    { key: "option", heading: "Option", isSortable: false },
    { key: "option_set", heading: "Option Set", isSortable: false },
    { key: "branding", heading: "Branding", isSortable: false },
    { key: "createdAt", heading: "Created At", isSortable: true, extraClass: "cursor-pointer" },
    { key: "status", heading: "Status", isSortable: false },
    { key: "actions", heading: "Actions", isSortable: false, extraClass: "text-center w-28" },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        {/* Table Head */}
        <thead className="text-xs text-gray-700 uppercase border-b-2 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-400">
          <tr>
            {tableHeaders.map((header) => (
              <th
                key={header.key}
                scope="col"
                className={`px-2 py-2 ${header?.extraClass}`}
              >
                {header.isSortable ? (
                  <SortableColumn
                    heading={header.heading}
                    name={header.key}
                    onSort={onSort}
                  />
                ) : (
                  header.heading
                )}
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {stores?.stores?.map((store: IShopData, index: number) => (
            <StoresTableData
              store={store}
              key={index}
              index={index}
              currentPage={currentPage}
              limit={limit}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}
