"use client";

import { TableDropdownActions, TTableDropdownActionOrSeperator } from "@/components/_components/TableDropdownActions";
import { Badge } from "@/components/ui/badge";
import { cancelSubscriptionAction, removeBrandingAction } from "@/modules/_actions/storeActions";
import { calculateDynamicId, formattedDate } from "@/utlis/helpers";
import { FootprintsIcon, InfoIcon, TicketXIcon, Webhook } from "lucide-react";
import Link from "next/link";
import toast from "react-hot-toast";
import OptionSetStatsInline from "./OptionSetStatsInline";
import OptionStatsInline from "./OptionStatsInline";

export default function StoresTableData({
  store,
  index,
  currentPage = 1,
  limit = 20,
}: {
  store: any;
  index: number;
  currentPage: number;
  limit: number;
}) {

  const handleRemoveBranding = async () => {
    const result = await removeBrandingAction(store?.domain);
    if (result.success) {
      toast.success(result.message);
    } else {
      toast.error(result.message);
    }
  };

  const handleCancelSubscription = async () => {
    const result = await cancelSubscriptionAction(store?.domain);
    if (result.success) {
      toast.success(result.message);
    } else {
      toast.error(result.message);
    }
  };

  const actions: TTableDropdownActionOrSeperator[] = [
    {
      title: "Store Details",
      Icon: InfoIcon,
      url: `/stores/${store?.domain}`,
      variant: "default",
    },
    { 
      title: "Plan Details", 
      Icon: InfoIcon, 
      url: `/stores/plan/${store?.domain}`,
      variant: "default" 
    },
    {
      title: "Webhook Details",
      Icon: Webhook,
      url: `/stores/webhook/${store?.domain}`,
      variant: "default",
    },
    {
      title: "Remove Branding",
      Icon: FootprintsIcon,
      variant: "warning",
      onAction: handleRemoveBranding,
      disabled: store?.isShowBranding === false,
    },
    { type: "seperator" },
    {
      title: "Cancel Subscription",
      Icon: TicketXIcon,
      variant: "warning",
      onAction: handleCancelSubscription,
      disabled: store?.isSubscriptionActive !== true,
    },
  ];

  const partnerUrl = `${process.env.NEXT_PUBLIC_PARTNER_APP_URL}/${store?.globalId?.split("/").pop()}`;

  return (
    <tr
      key={store.domain}
      className="border-b dark:border-gray-700"
    >
      <td className="px-2 py-1">{calculateDynamicId(index, currentPage, limit)}</td>
      <td className="px-2 py-1 min-w-52">
        <div className="flex items-center gap-1 ">
          Name:
          <Link
            className="hover:text-green-500"
            target="blank"
            href={`${store?.url}`}
          >
            {store?.name}
          </Link>
        </div>

        <div>
          Domain: <span className="hover:text-green-500 cursor-pointer">{store?.domain}</span>
        </div>

        <div className="">
          Email:{" "}
          <Link
            className="hover:text-green-500"
            href={`mailto:${store?.email}`}
          >
            {store?.email}
          </Link>
        </div>

        <div className="">
          Partner URL:{" "}
          <Link
            className="hover:text-green-500"
            href={partnerUrl}
            target="blank"
          >
            {partnerUrl}
          </Link>
        </div>
      </td>
      <td className="px-2 py-1">
        <Badge variant={store.isSubscriptionActive === true ? "success" : "warning"}>
          {store?.isSubscriptionActive === true ? "ACTIVE" : "WITHOUT PLAN"}
        </Badge>
      </td>
      <td className="px-2 py-1">
        <OptionStatsInline stats={store?.optionStats || { total: 0, withPrice: 0, withoutPrice: 0 }} />
      </td>
      <td className="px-2 py-1">
        <OptionSetStatsInline stats={store?.optionSetStats || { total: 0 }} />
      </td>
      <td className="px-2 py-1">
        <span className="text-[#c75108] uppercase">{store?.isShowBranding === false ? "Removed" : "Yes"}</span>
      </td>
      <td className="px-2 py-1">{formattedDate(store?.createdAt)}</td>
      {store?.status === "ACTIVE" ? (
        <td>
          <Badge
            size="regular"
            variant="success"
          >
            {store?.status}
          </Badge>
        </td>
      ) : (
        <td>
          <Badge
            size="regular"
            variant="danger"
          >
            {store?.status}
          </Badge>
        </td>
      )}

      {/* Action Button */}

      <td className="px-2 py-1 text-center">
        <TableDropdownActions
          label="Action"
          actions={actions}
        />
      </td>
    </tr>
  );
}
