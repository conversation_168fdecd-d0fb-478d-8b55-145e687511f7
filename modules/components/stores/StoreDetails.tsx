import AccordionWrapper from "@/components/_components/AccordionWrapper";
import DescriptionList from "@/components/_components/DescriptionList";
import datastore from "@/lib/datastore";
import { BillingAddressFormatter } from "@/modules/mappers/stores/billingAddressFormatter";
import storesDetailsViewFormatter from "@/modules/mappers/stores/storesMapper";
import { IShopData } from "@/types/stores";
import { isEmpty } from "lodash";

const StoreDetails = async ({ id }: { id: string }) => {
  const data: IShopData = await datastore.shop.getShopDetails(id);

  const storesDetails = storesDetailsViewFormatter(data);
  return (
    <>
      <DescriptionList listData={storesDetails} />

      <div className="flex flex-col gap-4 py-4">
        <AccordionWrapper
          title={"Shopify Plan"}
          background="bg-gray-50"
        >
          {isEmpty(data?.shopifyPlan) ? (
            "No Data Available"
          ) : (
            <div className="p-4 pb-0">
              <p>
                Display Name: <span className="text-sm leading-6 text-gray-700">{data?.shopifyPlan?.displayName}</span>
              </p>
              <p>
                Partner Development:{" "}
                <span className="text-sm leading-6 text-gray-700">{data?.shopifyPlan?.partnerDevelopment}</span>
              </p>
              <p>
                Shopify Plus: <span className="text-sm leading-6 text-gray-700">{data?.shopifyPlan?.shopifyPlus}</span>
              </p>
            </div>
          )}
        </AccordionWrapper>

        <AccordionWrapper
          title={"Billing Address"}
          background="bg-gray-50"
        >
          <div className="p-4 pb-0">
            {isEmpty(data?.billingAddress) ? (
              "No Data Available"
            ) : (
              <BillingAddressFormatter data={data?.billingAddress} />
            )}
          </div>
        </AccordionWrapper>
      </div>
    </>
  );
};

export default StoreDetails;
