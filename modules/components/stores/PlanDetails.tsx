import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import datastore from "@/lib/datastore";
import { format } from "date-fns";

export default async function PlanDetails({ domain }: { domain: string }) {
  // Get shop details from datastore
  const shopData = await datastore.shop.getShopDetails(domain);

  if (!shopData) {
    return (
      <div className="p-6 text-center">
        <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Shop Not Found</h3>
        <p className="text-gray-500">The shop with domain "{domain}" could not be found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Shop Basic Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Shop Information</h3>
        <div className="grid grid-cols-1 gap-3">
          <div>
            <label className="block text-sm font-medium text-gray-700">Shop Name</label>
            <p className="text-sm text-gray-900">{shopData.name || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Domain</label>
            <p className="text-sm text-gray-900">{shopData.domain || domain}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <p className="text-sm text-gray-900">{shopData.email || 'N/A'}</p>
          </div>
        </div>
      </div>

      {/* Subscription Status */}
      <div className="bg-white border rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Subscription Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Status</span>
            <Badge variant={shopData.planStatus === "ACTIVE" ? "success" : "warning"}>
              {shopData.planStatus || "INACTIVE"}
            </Badge>
          </div>

          {shopData.subscriptionPlan && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Plan</span>
              <span className="text-sm text-gray-900">{shopData.subscriptionPlan}</span>
            </div>
          )}

          {shopData.subscriptionStartDate && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Start Date</span>
              <span className="text-sm text-gray-900">
                {format(new Date(shopData.subscriptionStartDate), 'MMM dd, yyyy')}
              </span>
            </div>
          )}

          {shopData.planExpireDate && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Expires On</span>
              <span className="text-sm text-gray-900">
                {format(new Date(shopData.planExpireDate), 'MMM dd, yyyy')}
              </span>
            </div>
          )}

          {shopData.subscriptionEndDate && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">End Date</span>
              <span className="text-sm text-gray-900">
                {format(new Date(shopData.subscriptionEndDate), 'MMM dd, yyyy')}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Plan Details */}
      {shopData.planData && (
        <div className="bg-white border rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Plan Details</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Plan Name</span>
              <span className="text-sm text-gray-900">{shopData.planData.name}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Plan Type</span>
              <Badge variant={shopData.planData.type === "pro" ? "info" : "default"}>
                {shopData.planData.type?.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Plan Slug</span>
              <span className="text-sm text-gray-900">{shopData.planData.slug}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Price</span>
              <span className="text-sm text-gray-900">${shopData.planData.price}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Final Price</span>
              <span className="text-sm text-gray-900 font-medium">${shopData.planData.finalPrice}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Interval</span>
              <span className="text-sm text-gray-900">{shopData.planData.interval}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Duration</span>
              <span className="text-sm text-gray-900">{shopData.planData.duration} month(s)</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Trial Days</span>
              <span className="text-sm text-gray-900">{shopData.planData.trialDays} days</span>
            </div>
            {shopData.planData.discount !== "0" && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Discount</span>
                <Badge variant="success">{shopData.planData.discount}% OFF</Badge>
              </div>
            )}
            {shopData.planData.cappedAmount && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Capped Amount</span>
                <span className="text-sm text-gray-900">${shopData.planData.cappedAmount}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Plan Features */}
      {shopData.planFeatures && (
        <div className="bg-white border rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Plan Features</h3>
          <div className="grid grid-cols-1 gap-2">
            {Object.entries(shopData.planFeatures).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between py-1">
                <span className="text-sm text-gray-700 capitalize">
                  {key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <Badge variant={value ? "success" : "default"} size="sm">
                  {value ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Shopify Plan Details */}
      {shopData.shopifyPlan && (
        <div className="bg-white border rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Shopify Plan</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Display Name</span>
              <span className="text-sm text-gray-900">{shopData.shopifyPlan.displayName || 'N/A'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Partner Development</span>
              <Badge variant={shopData.shopifyPlan.partnerDevelopment ? "success" : "default"}>
                {shopData.shopifyPlan.partnerDevelopment ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Shopify Plus</span>
              <Badge variant={shopData.shopifyPlan.shopifyPlus ? "success" : "default"}>
                {shopData.shopifyPlan.shopifyPlus ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </div>
      )}

      {/* Additional Details */}
      <div className="bg-white border rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Additional Information</h3>
        <div className="space-y-3">
          {shopData.createdAt && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Created</span>
              <span className="text-sm text-gray-900">
                {format(new Date(shopData.createdAt), 'MMM dd, yyyy HH:mm')}
              </span>
            </div>
          )}

          {shopData.updatedAt && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Last Updated</span>
              <span className="text-sm text-gray-900">
                {format(new Date(shopData.updatedAt), 'MMM dd, yyyy HH:mm')}
              </span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Partner Development</span>
            <Badge variant={shopData.isPartnerDevelopment ? "warning" : "default"}>
              {shopData.isPartnerDevelopment ? "Development Store" : "Live Store"}
            </Badge>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline" size="sm">
          Refresh Data
        </Button>
        <Button size="sm">
          Update Plan
        </Button>
      </div>
    </div>
  );
}
