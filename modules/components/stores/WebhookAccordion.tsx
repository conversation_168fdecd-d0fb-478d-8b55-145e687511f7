import { IWebhookData } from "@/types/stores";

export default function WebhookAccordion({ data }: { data: IWebhookData }) {
  // const webhookDetails = webhookDetailsFormatter(data);

  return (
    <div className="overflow-auto">
      <>
        {/* {data ? (
          <AccordionWrapper
            title={data?.topic}
            background="bg-gray-50"
          >
            <AccordionContentList data={webhookDetails} />
          </AccordionWrapper>
        ) : (
          "No Webhooks Found"
        )} */}
      </>
    </div>
  );
}
