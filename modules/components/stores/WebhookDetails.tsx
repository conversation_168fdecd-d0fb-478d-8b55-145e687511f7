import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import datastore from "@/lib/datastore";
import { format } from "date-fns";

export default async function WebhookDetails({ domain }: { domain: string }) {
  // Get shop details from datastore
  const shopData = await datastore.shop.getShopDetails(domain);

  const webhooks: any[] = [];

  if (!shopData) {
    return (
      <div className="p-6 text-center">
        <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Shop Not Found</h3>
        <p className="text-gray-500">The shop with domain "{domain}" could not be found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Shop Info Header */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Webhook Management</h3>
        <div className="grid grid-cols-1 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700">Shop Name</label>
            <p className="text-sm text-gray-900">{shopData.name || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Domain</label>
            <p className="text-sm text-gray-900">{shopData.domain || domain}</p>
          </div>
        </div>
      </div>

      {/* Webhook Status */}
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Webhook Status</h3>
          <Badge variant="info">
            {webhooks.length} Webhooks
          </Badge>
        </div>

        {webhooks.length > 0 ? (
          <div className="space-y-3">
            {webhooks.map((webhook: any, index: number) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{webhook.topic}</span>
                  <Badge variant={webhook.status === 'active' ? 'success' : 'warning'}>
                    {webhook.status}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  <p>Endpoint: {webhook.endpoint}</p>
                  <p>Created: {format(new Date(webhook.createdAt), 'MMM dd, yyyy HH:mm')}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Webhooks Found</h3>
            <p className="text-gray-500 mb-4">
              No webhooks are currently registered for this shop.
            </p>
          </div>
        )}
      </div>

      {/* Webhook Information */}
      <div className="bg-white border rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Webhook Information</h3>
        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-700">Shop Status</span>
            <Badge variant={shopData.isSubscriptionActive ? "success" : "warning"}>
              {shopData.isSubscriptionActive ? "Active" : "Inactive"}
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-700">Development Store</span>
            <Badge variant={shopData.isPartnerDevelopment ? "warning" : "default"}>
              {shopData.isPartnerDevelopment ? "Yes" : "No"}
            </Badge>
          </div>

          {shopData.createdAt && (
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-700">Shop Created</span>
              <span className="text-gray-900">
                {format(new Date(shopData.createdAt), 'MMM dd, yyyy')}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline" size="sm">
          Refresh Webhooks
        </Button>
        <Button size="sm">
          Register Webhooks
        </Button>
      </div>
    </div>
  );
}
