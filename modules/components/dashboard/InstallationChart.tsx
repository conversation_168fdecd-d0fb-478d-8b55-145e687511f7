"use client";

import { IChartData, ISeries, TChartType } from "@/types/dashboard";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
const ChartWrapper = dynamic(() => import("@/modules/components/dashboard/ChartWrapper"), { ssr: false });

const chartOptions: IChartData = {
  series: [],
  options: {
    chart: {
      type: "bar",
      height: 400,
      stacked: true,
      toolbar: {
        show: true,
      },
      colors: ["#597dfc", "#60ce83", "#ff9635", "#ff5f74", "#2fc1e1", "#ff61d3"],
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
    },
    responsive: [
      {
        options: {
          legend: {
            position: "bottom",
            offsetX: -10,
            offsetY: 0,
          },
        },
      },
    ],
    xaxis: {
      labels: {
        show: true,
        rotate: -45,
        rotateAlways: true,
        minHeight: 100,
        maxHeight: 180,
      },
      type: "category",
    },
    legend: {
      show: true,
      position: "bottom",
      markers: {
        shape: "square",
      },
    },
    fill: {
      opacity: 1,
    },
  },
};

export default function InstallationChart({ series }: { series: ISeries[] }) {
  const [chartState, setChartState] = useState<IChartData>(chartOptions);

  useEffect(() => {
    if (series) {
      setChartState((old) => ({ ...old, series: series }));
    }
  }, [series]);

  return (
    <>
      <ChartWrapper
        state={chartState}
        height={chartOptions.options?.chart?.height}
        type={chartOptions.options?.chart?.type as TChartType}
      />
    </>
  );
}
