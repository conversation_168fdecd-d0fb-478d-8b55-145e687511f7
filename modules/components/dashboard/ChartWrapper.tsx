import { IChartData, TChartType } from "@/types/dashboard";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"));

export default function ChartWrapper({ state, height, type }: { state: IChartData; height: number; type: TChartType }) {
  return (
    <>
      <div className="rounded-lg bg-white shadow p-4 min-h-96">
        {typeof window !== "undefined" && (
          <Chart
            options={state.options}
            series={state.series}
            type={type}
            height={height}
            width="100%"
          />
        )}
      </div>
    </>
  );
}
