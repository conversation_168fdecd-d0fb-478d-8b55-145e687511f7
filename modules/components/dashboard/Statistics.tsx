import datastore from "@/lib/datastore";
import { unstable_cache as cache } from "next/cache";
import StatisticsCard from "./StatisticsCard";

export default async function Statistics() {
  const stores = await cache(() => datastore.shop.getShopsCount(), [], {
    revalidate: 3600,
    tags: ["statistics"],
  })();

  return (
    <div className="mt-1 grid grid-cols-2 divide-x divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow lg:grid-cols-6 lg:divide-x lg:divide-y-0">
      {stores?.map((item: any, index: number) => (
        <StatisticsCard
          key={index}
          name={item.title}
          stat={item.value}
        />
      ))}
    </div>
  );
}
