/* eslint-disable no-unused-vars */
"use client";

import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/modules/hooks/useAuth";
import { useGravater } from "@/modules/hooks/useGravatar";
import { TAdmin } from "@/types/admin";
import { FilePenLine, KeyRoundIcon, Trash2, UserCogIcon } from "lucide-react";
import Image from "next/image";
import { useMemo } from "react";
import AdminCardAction from "./AdminCardAction";

export default function AdminCard({
  admin,
  onDelete,
  onUpdate,
  onChangePassword,
  onUpdatePermission,
  hasDeletePermission,
  hasWritePermission,
}: {
  admin: TAdmin;
  onDelete: React.ReactEventHandler;
  onUpdate: React.ReactEventHandler;
  onChangePassword: React.ReactEventHandler;
  onUpdatePermission: React.ReactEventHandler;
  hasDeletePermission: boolean;
  hasWritePermission: boolean;
}) {
  const avaterUrl = useGravater(admin.email, 200);

  const { user } = useAuth();

  const isLoggedIn = admin?.email === user?.email;

  const isAdmin = user?.type === 1 || user?.type === 2;

  const roleVarient = useMemo(() => {
    switch (admin?.role) {
      case "SUPER_ADMIN":
        return "success";
      case "ADMIN":
        return "info";
      case "TESTING":
      case "MARKETING":
      case "SUPPORT":
        return "warning";
      default:
        return "default";
    }
  }, [admin?.role]);

  return (
    <div className="col-span-1 flex flex-col divide-y divide-gray-200 rounded-lg bg-white text-center shadow h-full">
      <div className="flex flex-1 flex-col p-8 items-center">
        <Image
          className="h-32 w-32 bg-gray-100 p-1 rounded-full"
          width={100}
          height={100}
          src={avaterUrl}
          alt={admin.name}
        />
        <h3 className="mt-3 text-sm font-medium text-gray-900">{admin.name}</h3>
        <p className="text-sm mt-2 text-gray-500">{admin.email}</p>
        <div className="mt-2">
          <Badge
            className="capitalize"
            variant={roleVarient}
          >
            {admin?.role?.replace("_", " ")?.toLowerCase()}
          </Badge>
        </div>
      </div>

      <div>
        <div className="-mt-px h-10 flex divide-x divide-gray-200">
          <AdminCardAction
            Icon={FilePenLine}
            onAction={isAdmin || isLoggedIn ? onUpdate : () => {}}
            tooltipText={isAdmin || isLoggedIn ? "Update Admin" : "Insufficient permission"}
            variant={isAdmin || isLoggedIn ? "info" : "disabled"}
            disabled={!isAdmin && !isLoggedIn}
            withHoverBackground
          />

          <AdminCardAction
            Icon={KeyRoundIcon}
            onAction={isAdmin || isLoggedIn ? onChangePassword : () => {}}
            tooltipText={isAdmin || isLoggedIn ? "Change Password" : "Insufficient permission"}
            variant={isAdmin || isLoggedIn ? "warning" : "disabled"}
            disabled={!isLoggedIn && !isAdmin}
            withHoverBackground
          />

          <AdminCardAction
            Icon={UserCogIcon}
            onAction={isAdmin ? onUpdatePermission : () => {}}
            tooltipText={isAdmin ? "Update Permissions" : "Insufficient permission"}
            variant={isAdmin ? "info" : "disabled"}
            disabled={!isAdmin}
            withHoverBackground
          />

          <AdminCardAction
            Icon={Trash2}
            onAction={isAdmin && !isLoggedIn ? onDelete : () => {}}
            tooltipText={isAdmin && !isLoggedIn ? "Delete" : "Insufficient permission"}
            variant={isAdmin && !isLoggedIn ? "danger" : "disabled"}
            disabled={!isAdmin || isLoggedIn}
            withHoverBackground
          />
        </div>
      </div>
    </div>
  );
}
