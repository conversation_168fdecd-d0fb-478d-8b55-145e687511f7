import InputField from "@/components/_components/InputField";
import Modal from "@/components/_components/Modal";
import { updatePasswordAction } from "@/modules/_actions/adminActions";
import { UpdatePasswordSchema } from "@/modules/validations/admins";
import { TAdminPassword } from "@/types/admin";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function ChangePasswordModal({ updatePasswordModal, setUpdatePasswordModal, admin }: any) {
  const [formError, setFormError] = useState<string | null>(null);
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<TAdminPassword>({
    resolver: zodResolver(UpdatePasswordSchema),
  });

  const onSubmit = async (data: TAdminPassword) => {
    const response = await updatePasswordAction(admin?.id, data);
    if (response.success) {
      reset();
      toast.success(response.message);
      setUpdatePasswordModal(false);
    } else {
      setFormError(response?.message as string);
    }
  };

  useEffect(() => {
    if (!updatePasswordModal) {
      reset();
      setFormError(null);
    }
  }, [reset, updatePasswordModal]);

  return (
    <Modal
      modalTitle="Change Password"
      handlePrimaryAction={handleSubmit(onSubmit)}
      primaryActionText={"Change"}
      modalSize="sm"
      open={updatePasswordModal}
      setOpen={(isOpen) => setUpdatePasswordModal(isOpen)}
      loading={isSubmitting}
    >
      <div className="flex flex-col gap-1">
        <p className="text-sm">
          <span className="font-normal text-gray-500 inline-block min-w-[60px]">Name: </span>
          <span className="font-medium  text-gray-600">{admin?.name}</span>
        </p>
        <p className="text-sm">
          <span className="font-normal text-gray-500 inline-block min-w-[60px]">Email: </span>
          <span className="font-medium text-gray-600">{admin?.email}</span>
        </p>
      </div>

      <form className="flex flex-col gap-4 mt-6">
        {formError && <p className="text-red-500 text-sm text-center">{formError}</p>}
        <div>
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <InputField
                label="New Password"
                type="password"
                placeholder="Enter Your New Password"
                error={errors?.password?.message}
                {...field}
              />
            )}
          />
        </div>

        <div>
          <Controller
            name="confirmPassword"
            control={control}
            render={({ field }) => (
              <InputField
                label="Confirm Password"
                required
                type="password"
                placeholder="Re-enter your password"
                error={errors?.confirmPassword?.message}
                {...field}
              />
            )}
          />
        </div>
      </form>
    </Modal>
  );
}
