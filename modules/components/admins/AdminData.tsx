"use client";

import NoData from "@/components/_components/NoData";
import SearchBar from "@/components/_components/SearchBar";
import TooltipWrapper from "@/components/_components/TooltipWrapper";
import { Button } from "@/components/ui/button";
import { deleteAdminAction, getAdminAction } from "@/modules/_actions/adminActions";
import { useConfirm } from "@/modules/hooks/useConfirm";
import useUserPermission from "@/modules/hooks/useUserPermission";
import { TAdmin } from "@/types/admin";
import { isEmpty } from "lodash";
import { useState } from "react";
import toast from "react-hot-toast";
import AddAdminModal from "./AddAdminModal";
import AdminCard from "./AdminCard";
import ChangePasswordModal from "./ChangePasswordModal";
import UpdateAdminModal from "./UpdateAdminModal";
import UpdatePermissionTable from "./UpdatePermissionTable";

export default function AdminTable({ admins }: { admins: TAdmin[] }) {
  const [addAdminModal, setAddAdminModal] = useState(false);
  const [updateAdminModal, setUpdateAdminModal] = useState(false);
  const [updatePasswordModal, setUpdatePasswordModal] = useState(false);
  const [permissionModal, setPermissionModal] = useState(false);
  const [adminData, setAdminData] = useState<TAdmin | null>(null);
  const [id, setId] = useState<number | null>(null);

  const { ConfirmAlert, showConfirm } = useConfirm();

  const { hasWritePermission, hasDeletePermission } = useUserPermission(["ADMIN_READ", "ADMIN_WRITE", "ADMIN_DELETE"]);

  const handleUpdate = async (id: number) => {
    setUpdateAdminModal(true);
    const response = await getAdminAction(id);
    if (response.success) {
      setAdminData(response.data as TAdmin);
    }
  };

  const handleDelete = async () => {
    const response = await deleteAdminAction(id);

    if (response.success) {
      toast.success(response.message);
    } else {
      toast.error(response?.message as string);
    }
  };

  const handleChangePassword = async (id: number) => {
    setUpdatePasswordModal(true);
    const response = await getAdminAction(id);
    if (response.success) {
      setAdminData(response.data as TAdmin);
    }
  };

  const handleUpdatePermission = async (id: number) => {
    setPermissionModal(true);
    const response = await getAdminAction(id);
    if (response.success) {
      setAdminData(response.data as TAdmin);
    }
  };
  return (
    <>
      <div className="flex justify-between items-center w-full mb-5">
        <h2 className="text-md font-medium">Admins</h2>
        <TooltipWrapper tooltipText={hasWritePermission || hasDeletePermission ? null : "Insufficient permissions"}>
          <Button
            onClick={hasWritePermission || hasDeletePermission ? () => setAddAdminModal(true) : () => {}}
            size="sm"
            className={hasWritePermission || hasDeletePermission ? "" : "opacity-50 cursor-not-allowed"}
          >
            Add Admin
          </Button>
        </TooltipWrapper>
      </div>

      {/* Admin Search */}
      <div className="p-2 bg-white rounded-lg mb-2 shadow">
        <SearchBar
          placeholder="Name/Email"
          setCurrentPage={() => {}}
        />
      </div>

      {isEmpty(admins) ? (
        <NoData />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
          <>
            {admins?.map((admin) => (
              <AdminCard
                admin={admin}
                key={admin?.id}
                onUpdate={() => handleUpdate(admin?.id)}
                onChangePassword={() => handleChangePassword(admin?.id)}
                onUpdatePermission={() => handleUpdatePermission(admin?.id)}
                hasDeletePermission={hasDeletePermission}
                hasWritePermission={hasWritePermission}
                onDelete={() => {
                  showConfirm();
                  setId(admin?.id);
                }}
              />
            ))}
          </>
        </div>
      )}

      <ConfirmAlert
        title="Are you sure?"
        content="You want to delete this admin? The action cannot be undone."
        confirmAction={{
          onAction: handleDelete,
          label: "Yes, Delete",
          variant: "danger",
          loading: false,
        }}
      />

      <AddAdminModal
        addAdminModal={addAdminModal}
        setAddAdminModal={setAddAdminModal}
      />

      <UpdateAdminModal
        updateAdminModal={updateAdminModal}
        setUpdateAdminModal={setUpdateAdminModal}
        admin={adminData}
      />

      <ChangePasswordModal
        updatePasswordModal={updatePasswordModal}
        setUpdatePasswordModal={setUpdatePasswordModal}
        admin={adminData}
      />

      <UpdatePermissionTable
        updatePermissionModal={permissionModal}
        setUpdatePermissionModal={setPermissionModal}
        admin={adminData as TAdmin}
      />
    </>
  );
}
