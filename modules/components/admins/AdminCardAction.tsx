import { cn } from "@/lib/utils";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Variant, useVariant } from "@/modules/hooks/useVariant";
import { useRouter } from "next/navigation";
import React, { ReactEventHandler, useCallback } from "react";

type TAdminCardAction = {
  Icon: React.ComponentType<{
    className?: string;
  }>;
  onAction?: ReactEventHandler;
  tooltipText?: string;
  variant?: Variant;
  url?: string;
  withHoverBackground?: boolean;
  disabled?: boolean;
};

export default function AdminCardAction({
  Icon,
  onAction = () => {},
  tooltipText = "",
  variant = "default",
  url = "",
  withHoverBackground = false,
  disabled = false,
}: TAdminCardAction) {
  const { textColor } = useVariant(variant);
  const router = useRouter();

  const handleClick = useCallback(
    (e: React.SyntheticEvent) => {
      if (url) {
        router.push(url);
      } else {
        onAction(e);
      }
    },
    [url, router, onAction]
  );

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger
          asChild
          onClick={handleClick}
          className={cn(disabled ? "cursor-not-allowed" : "cursor-pointer")}
        >
          <div
            className={cn(
              "relative -mr-px inline-flex w-fit flex-1 items-center justify-center gap-x-3 font-semibold text-gray-900",
              withHoverBackground && !disabled && "hover:bg-gray-200",
              disabled && "text-gray-400"
            )}
          >
            <Icon
              className={cn("w-4 h-4", textColor)}
              aria-hidden="true"
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
