/* eslint-disable no-unused-vars */
import TooltipWrapper from "@/components/_components/TooltipWrapper";
import { Checkbox } from "@/components/ui/checkbox";
import { TPermission } from "@/types/admin";
import UpdatePermissionData from "./UpdatePermissionData";

export default function UpdatePermissionBody({
  handlePermissionChange,
  permissions,
  selectedPermissions,
  handleAllChange,
}: {
  handlePermissionChange: (feature: string) => void;
  permissions: TPermission[];
  selectedPermissions: string[];
  handleAllChange: (action: string, checked: boolean) => void;
}) {
  const allViewChecked = permissions
    ?.flatMap((permission) => permission?.features?.filter((feature) => feature?.includes("READ")))
    ?.every((feature) => selectedPermissions?.includes(feature));

  const allEditChecked = permissions
    ?.flatMap((permission) => permission?.features?.filter((feature) => feature?.includes("WRITE")))
    ?.every((feature) => selectedPermissions?.includes(feature));

  const allDeleteChecked = permissions
    ?.flatMap((permission) => permission?.features?.filter((feature) => feature?.includes("DELETE")))
    ?.every((feature) => selectedPermissions?.includes(feature));

  return (
    <div className="flex flex-col">
      <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 uppercase border-b-2 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-400">
          <tr>
            <th
              scope="col"
              className="px-4 py-3 h-[46px]"
            >
              Feature
            </th>
            <th
              scope="col"
              className="px-4 py-3 w-20 text-center"
            >
              <div className="flex items-center justify-center gap-2">
                <TooltipWrapper
                  textSize="xs"
                  tooltipText={allViewChecked ? "Deselect All" : "Select All"}
                >
                  <Checkbox
                    checked={allViewChecked}
                    onCheckedChange={(checked) => handleAllChange("READ", checked as boolean)}
                  />
                </TooltipWrapper>
                <span>View</span>
              </div>
            </th>

            <th
              scope="col"
              className="px-4 py-3 w-20 text-right"
            >
              <div className="flex items-center justify-center gap-2">
                <TooltipWrapper
                  textSize="xs"
                  tooltipText={allEditChecked ? "Deselect All" : "Select All"}
                >
                  <Checkbox
                    checked={allEditChecked}
                    onCheckedChange={(checked) => handleAllChange("WRITE", checked as boolean)}
                  />
                </TooltipWrapper>
                <span>Edit</span>
              </div>
            </th>

            <th
              scope="col"
              className="px-4 py-3 w-20 text-center"
            >
              <div className="flex items-center justify-center gap-2">
                <TooltipWrapper
                  textSize="xs"
                  tooltipText={allDeleteChecked ? "Deselect All" : "Select All"}
                >
                  <Checkbox
                    checked={allDeleteChecked}
                    onCheckedChange={(checked) => handleAllChange("DELETE", checked as boolean)}
                  />
                </TooltipWrapper>
                <span>Delete</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {permissions?.map((permission: TPermission, index: number) => (
            <UpdatePermissionData
              key={index}
              permission={permission}
              handlePermissionChange={handlePermissionChange}
              selectedPermissions={selectedPermissions}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}
