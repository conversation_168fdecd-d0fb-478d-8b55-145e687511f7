import DropdownWrapper from "@/components/_components/DropdownWrapper";
import InputField from "@/components/_components/InputField";
import Modal from "@/components/_components/Modal";
import { ADMIN_ROLES } from "@/config/admins";
import { updateAdminAction } from "@/modules/_actions/adminActions";
import { useAuth } from "@/modules/hooks/useAuth";
import { UpdateAdminSchema } from "@/modules/validations/admins";
import { TUpdateAdmin } from "@/types/admin";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";

export default function UpdateAdminModal({ updateAdminModal, setUpdateAdminModal, admin }: any) {
  const [formError, setFormError] = useState<string | null>(null);

  const roles = Object.entries(ADMIN_ROLES).map(([, value]) => ({
    value: value.value,
    label: value.label,
  }));

  const onSubmit = async (data: TUpdateAdmin) => {
    const response = await updateAdminAction(admin?.id, data);

    if (response?.success) {
      reset();
      toast.success(response.message);
      setUpdateAdminModal(false);
    } else {
      setFormError(response.message);
    }
  };

  const { user } = useAuth();
  const hasRoleUpdatePermission = [1, 2].includes(user?.type as number);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<TUpdateAdmin>({
    resolver: zodResolver(UpdateAdminSchema),
    values: {
      name: admin?.name,
      email: admin?.email,
      type: admin?.type,
    },
  });

  useEffect(() => {
    if (!updateAdminModal) {
      reset();
      setFormError(null);
    }
  }, [reset, updateAdminModal]);

  return (
    <Modal
      modalTitle="Update Admin"
      handlePrimaryAction={handleSubmit(onSubmit)}
      primaryActionText={"Update"}
      modalSize="sm"
      open={updateAdminModal}
      setOpen={(isOpen) => setUpdateAdminModal(isOpen)}
      loading={isSubmitting}
    >
      <form className="flex flex-col gap-4">
        {formError && <p className="text-red-500 text-sm text-center">{formError}</p>}

        {/* name */}
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <InputField
              label="Name"
              placeholder="Enter Your Name"
              error={errors?.name?.message}
              {...field}
            />
          )}
        />

        {/* email */}
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <InputField
              label="Email"
              placeholder="Enter Your Email"
              error={errors?.email?.message}
              {...field}
            />
          )}
        />

        {/* Admin Role */}
        <Controller
          name="type"
          control={control}
          render={({ field }) => (
            <DropdownWrapper
              label="Select Admin Role"
              field={field}
              items={roles}
              title="Select Admin Role"
              disabled={!hasRoleUpdatePermission}
              error={errors?.type?.message}
            />
          )}
        />
      </form>
    </Modal>
  );
}
