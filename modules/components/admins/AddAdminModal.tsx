import DropdownWrapper from "@/components/_components/DropdownWrapper";
import InputField from "@/components/_components/InputField";
import Modal from "@/components/_components/Modal";
import { Label } from "@/components/ui/label";
import { ADMIN_ROLES, adminScopes } from "@/config/admins";
import { addAdminAction } from "@/modules/_actions/adminActions";
import { ROLE_PERMISSIONS, RoleName } from "@/modules/mappers/adminRolesPermission";
import { permissionScopes } from "@/modules/mappers/permissionScopesMapper";
import { AddAdminSchema } from "@/modules/validations/admins";
import { TAddAdmin } from "@/types/admin";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import UpdatePermissionBody from "./UpdatePermissionBody";

export default function AddAdminModal({
  addAdminModal,
  setAddAdminModal,
}: {
  addAdminModal: boolean;
  setAddAdminModal: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [formError, setFormError] = useState<string | null>(null);

  const permissionArr = permissionScopes(adminScopes);
  const {
    control,
    formState: { errors, isSubmitting },
    handleSubmit,
    watch,
    reset,
  } = useForm<TAddAdmin>({
    resolver: zodResolver(AddAdminSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      type: null,
    },
  });

  const handlePermissionChange = (feature: string) => {
    setSelectedPermissions((prevPermissions) => {
      const updatedPermissions = [...prevPermissions];
      const permissionIndex = updatedPermissions.indexOf(feature);

      if (permissionIndex === -1) {
        updatedPermissions.push(feature);
      } else {
        updatedPermissions.splice(permissionIndex, 1);
      }

      return updatedPermissions;
    });
  };

  const handleAllChange = (action: string, checked: boolean) => {
    setSelectedPermissions((prevPermissions) => {
      const actionPermissions = permissionArr
        .flatMap((permission) => permission.features)
        .filter((feature) => feature.includes(action));

      if (checked) {
        return Array.from(new Set([...prevPermissions, ...actionPermissions]));
      } else {
        return prevPermissions.filter((perm) => !actionPermissions.includes(perm));
      }
    });
  };

  const roles = Object.entries(ADMIN_ROLES).map(([, value]) => ({
    value: value.value,
    label: value.label,
  }));

  const onSubmit = async (data: TAddAdmin) => {
    if (data && selectedPermissions) {
      const payload = {
        ...data,
        scopes: JSON.stringify(selectedPermissions),
      };

      const response = await addAdminAction(payload);

      if (response?.success) {
        toast.success(response.message);
        setAddAdminModal(false);
        reset();
      } else {
        setFormError(response.message);
      }
    }
  };

  const selectedType = watch("type");

  useEffect(() => {
    if (!addAdminModal) {
      reset();
      setSelectedPermissions([]);
      setFormError(null);
    }
  }, [addAdminModal, reset]);

  useEffect(() => {
    if (selectedType) {
      const roleName = (Object.keys(ROLE_PERMISSIONS) as RoleName[]).find(
        (key) => ROLE_PERMISSIONS[key]?.type === selectedType
      );

      const rolePermissions = roleName ? ROLE_PERMISSIONS[roleName]?.permission : [];
      setSelectedPermissions(rolePermissions);
    }
  }, [selectedType]);

  return (
    <>
      <Modal
        modalTitle="Add Admin"
        handlePrimaryAction={handleSubmit(onSubmit)}
        primaryActionText={"Create"}
        modalSize="md"
        open={addAdminModal}
        setOpen={(isOpen) => setAddAdminModal(isOpen)}
        loading={isSubmitting}
      >
        <form>
          {formError && <p className="text-red-500 text-sm my-2 text-center">{formError}</p>}
          <div className="grid grid-cols-2 gap-4">
            {/* Name */}
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Name"
                  type="text"
                  placeholder="Enter Your Name"
                  error={errors?.name?.message}
                  {...field}
                />
              )}
            />

            {/* Email */}
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Email"
                  type="email"
                  placeholder="Enter Your Email"
                  error={errors?.email?.message}
                  {...field}
                />
              )}
            />

            {/* Password */}
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Password"
                  type="password"
                  placeholder="Enter Your Password"
                  error={errors?.password?.message}
                  {...field}
                />
              )}
            />

            {/* Confirm Password */}
            <Controller
              name="confirmPassword"
              control={control}
              render={({ field }) => (
                <InputField
                  label="Confirm Password"
                  type="password"
                  placeholder="Enter Confirm Password"
                  error={errors?.confirmPassword?.message}
                  {...field}
                />
              )}
            />

            {/* Role Type */}
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <DropdownWrapper
                  label="Select Admin Role"
                  field={field}
                  items={roles}
                  title="Select Admin Role"
                  error={errors?.type?.message}
                  {...field}
                />
              )}
            />
          </div>

          <div className="mt-3">
            <Label className="text-[13px] text-gray-500 font-normal">
              Admin Permissions <span className="text-red-500"> *</span>
            </Label>
            <UpdatePermissionBody
              handlePermissionChange={handlePermissionChange}
              permissions={permissionArr}
              selectedPermissions={selectedPermissions}
              handleAllChange={handleAllChange}
            />
          </div>
        </form>
      </Modal>
    </>
  );
}
