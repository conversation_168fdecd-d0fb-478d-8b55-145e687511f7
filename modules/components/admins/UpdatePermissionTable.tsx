import Modal from "@/components/_components/Modal";
import { adminScopes } from "@/config/admins";
import { updatePermissionAction } from "@/modules/_actions/adminActions";
import { permissionScopes } from "@/modules/mappers/permissionScopesMapper";
import { TAdmin } from "@/types/admin";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import UpdatePermissionBody from "./UpdatePermissionBody";

type TUpdatePermission = {
  updatePermissionModal: boolean;
  setUpdatePermissionModal: React.Dispatch<React.SetStateAction<boolean>>;
  admin?: TAdmin;
};

export default function UpdatePermissionTable({
  updatePermissionModal,
  setUpdatePermissionModal,
  admin,
}: TUpdatePermission) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const handlePermissionChange = (feature: string) => {
    setSelectedPermissions((prevPermissions) => {
      const updatedPermissions = [...prevPermissions];
      const permissionIndex = updatedPermissions.indexOf(feature);

      if (permissionIndex === -1) {
        updatedPermissions.push(feature);
      } else {
        updatedPermissions.splice(permissionIndex, 1);
      }

      return updatedPermissions;
    });
  };

  const handleAllChange = (action: string, checked: boolean) => {
    setSelectedPermissions((prevPermissions) => {
      const actionPermissions = permissionArr
        .flatMap((permission) => permission.features)
        .filter((feature) => feature.includes(action));

      if (checked) {
        return Array.from(new Set([...prevPermissions, ...actionPermissions]));
      } else {
        return prevPermissions?.filter((perm) => !actionPermissions.includes(perm));
      }
    });
  };

  const permissionArr = permissionScopes(adminScopes);

  const handleModalSubmit = async () => {
    const response = await updatePermissionAction(admin?.id as number, selectedPermissions);

    if (response.success) {
      setUpdatePermissionModal(false);
      toast.success(response.message);
    }
  };

  useEffect(() => {
    if (admin?.scopes) {
      setSelectedPermissions(JSON.parse(admin?.scopes));
    }
  }, [admin?.scopes]);

  return (
    <>
      <Modal
        modalTitle="Update Permissions"
        handlePrimaryAction={handleModalSubmit}
        primaryActionText={"Update"}
        modalSize="md"
        open={updatePermissionModal}
        setOpen={(isOpen) => setUpdatePermissionModal(isOpen)}
        // loading={isPending}
      >
        <div className="flex flex-col gap-1 mb-6">
          <p className="text-sm">
            <span className="font-normal text-gray-500 inline-block min-w-[60px]">Name: </span>
            <span className="font-medium  text-gray-600">{admin?.name}</span>
          </p>
          <p className="text-sm">
            <span className="font-normal text-gray-500 inline-block min-w-[60px]">Email: </span>
            <span className="font-medium text-gray-600">{admin?.email}</span>
          </p>
        </div>

        <UpdatePermissionBody
          handlePermissionChange={handlePermissionChange}
          permissions={permissionArr}
          selectedPermissions={selectedPermissions}
          handleAllChange={handleAllChange}
        />
      </Modal>
    </>
  );
}
