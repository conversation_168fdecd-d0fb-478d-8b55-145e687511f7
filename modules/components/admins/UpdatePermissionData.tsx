import { Checkbox } from "@/components/ui/checkbox";
import { TPermission } from "@/types/admin";

export default function UpdatePermissionData({
  permission,
  handlePermissionChange,
  selectedPermissions,
}: {
  permission: TPermission;
  // eslint-disable-next-line no-unused-vars
  handlePermissionChange: (feature: string) => void;
  selectedPermissions: string[];
}) {
  const viewFeature = permission?.features.find((feature) => feature?.includes("READ"));
  const editFeature = permission?.features.find((feature) => feature?.includes("WRITE"));
  const deleteFeature = permission?.features.find((feature) => feature?.includes("DELETE"));

  return (
    <tr className="border-b dark:border-gray-700 last:border-b-0">
      <td className="px-4 py-3 h-[49px]">{permission?.name}</td>
      <td className="px-4 py-3 text-center">
        <Checkbox
          checked={selectedPermissions?.includes(viewFeature as string)}
          onCheckedChange={() => handlePermissionChange(viewFeature as string)}
          hidden={!viewFeature}
        />
      </td>
      <td className="px-4 py-3 text-center">
        <Checkbox
          checked={selectedPermissions?.includes(editFeature as string)}
          onCheckedChange={() => handlePermissionChange(editFeature as string)}
          hidden={!editFeature}
        />
      </td>
      <td className="px-4 py-3 text-center">
        <Checkbox
          checked={selectedPermissions?.includes(deleteFeature as string)}
          onCheckedChange={() => handlePermissionChange(deleteFeature as string)}
          hidden={!deleteFeature}
        />
      </td>
    </tr>
  );
}
