"use server";

import { add<PERSON><PERSON>, deletePlan, getPlan, updatePlan } from "@/lib/plan.lib";
import { IAddSubscriptionPlan } from "@/types/plans";
import { getFirstZodError, handlePrismaError } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";
import { revalidateTag } from "next/cache";
import { AddPlanSchema, UpdatePlanSchema } from "../validations/plans";

export async function addPlanAction(data: IAddSubscriptionPlan) {
  try {
    const validated = AddPlanSchema.safeParse(data);

    if (validated.success) {
      const plan = await addPlan(validated.data);

      if (!plan) {
        return { success: false, message: "Plan create failed" };
      }
      revalidateTag("plans");
      if (data?.id) {
        return { success: true, message: "Plan copied" };
      }
      return { success: true, message: "Plan created" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Duplicate slug found");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}

export async function getPlanAction(id: number) {
  try {
    const plan = await getPlan(id);

    if (!plan) {
      return { success: false, message: "Plan not found", data: {} };
    }

    return { success: true, message: "Plan Found", data: plan };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function deletePlanAction(id: number | null) {
  try {
    if (!id) {
      return { success: false, message: "Plan ID is required" };
    }
    const result = await deletePlan(id);

    if (!result) {
      return { success: false, message: "Plan not found" };
    }

    revalidateTag("plans");
    return { success: true, message: "Plan deleted" };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function updatePlanAction(id: number | null, data: IAddSubscriptionPlan) {
  try {
    if (!id) {
      return { success: false, message: "Plan ID is required" };
    }
    const validated = UpdatePlanSchema.safeParse(data);

    if (validated.success) {
      await updatePlan(id, validated.data);
      revalidateTag("plans");

      return { success: true, message: "Plan updated" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Duplicate slug found");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}
