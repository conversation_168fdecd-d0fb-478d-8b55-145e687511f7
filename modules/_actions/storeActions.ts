'use server'

import datastore from "@/lib/datastore";
import { revalidatePath } from "next/cache";

export async function removeBrandingAction(domain: string) {
  try {
    if (!domain) {
      return { success: false, message: "Store domain is required" };
    }

    // Get the shop data
    const shopData = await datastore.shop.getShopDetails(domain);
    
    if (!shopData) {
      return { success: false, message: "Store not found" };
    }

    // Check if branding is already removed
    if (shopData.isShowBranding === false) {
      return { success: false, message: "Branding is already removed for this store" };
    }

    // Update the shop to remove branding
    const shopKey = datastore.client.key(["Shop", domain]);
    
    const updatedShop = {
      key: shopKey,
      data: {
        ...shopData,
        isShowBranding: false,
        updatedAt: new Date().toISOString(),
      },
    };

    await datastore.client.save(updatedShop);

    // Revalidate the stores page to show updated data
    revalidatePath('/stores');

    return {
      success: true,
      message: 'Branding removed successfully'
    };
  } catch (error) {
    console.error('Error removing branding:', error);
    return { 
      success: false, 
      message: 'Failed to remove branding' 
    };
  }
}

export async function cancelSubscriptionAction(domain: string) {
  try {
    if (!domain) {
      return { success: false, message: "Store domain is required" };
    }

    console.log('Cancel subscription for', domain);

    // Call your Shopify app's cancel-subscription API
    const response = await fetch(`${process.env.MAIN_APP_BASE_URL}/cancel-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ shop: domain }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      // Revalidate the stores page to show updated data
      revalidatePath('/stores');

      return {
        success: true,
        message: result.message || 'Subscription cancelled successfully'
      };
    } else {
      return {
        success: false,
        message: result.message || 'Failed to cancel subscription'
      };
    }
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return {
      success: false,
      message: 'Failed to cancel subscription'
    };
  }
}
