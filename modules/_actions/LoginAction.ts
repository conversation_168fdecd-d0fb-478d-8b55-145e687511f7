"use server";

import { prisma } from "@/lib/db";
import { createSession } from "@/lib/session";
import { LoginFormValues } from "@/types/common";
import { exclude } from "@/utlis/helpers";
import bcrypt from "bcrypt";
import { LoginActionSchema } from "../validations/login";

const isPasswordMatch = async (password: string, userPassword: string) => {
  return bcrypt.compare(password, userPassword);
};

export async function loginAction(data: LoginFormValues) {
  try {
    const validated = LoginActionSchema.safeParse(data);

    if (validated.success) {
      const { email, password } = validated.data;

      const user = await prisma.admin.findUnique({
        where: { email: email },
      });

      if (!user || !(await isPasswordMatch(password, user.password))) {
        return { success: false, message: "Email or password is incorrect" };
      }

      await createSession(user.id, user.email, user.scopes as string);

      return { success: true, message: "Login Successfull", data: exclude(user, ["password"]) };
    }
    // return { success: false, message: convertZodErrors(validated.error) };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: "An unknown error occurred" };
  }
}
