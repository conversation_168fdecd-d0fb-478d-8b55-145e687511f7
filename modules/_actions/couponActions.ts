"use server";

import { addC<PERSON>pon, deleteC<PERSON>pon, getCoupon, updateCoupon } from "@/lib/coupon.lib";
import { IAddCoupon, IUpdateCoupon } from "@/types/coupons";
import { getFirstZodError, handlePrismaError } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";
import { revalidateTag } from "next/cache";
import { AddCouponSchema, UpdateCouponSchema } from "../validations/coupons";

export async function addCouponAction(data: IAddCoupon) {
  try {
    const validated = AddCouponSchema.safeParse(data);

    if (validated.success) {
      if (data?.plans && data.plans.length === 0) {
        return { success: false, message: "At least one plan is required" };
      }

      const coupon = await addCoupon(validated.data);

      if (!coupon) {
        return { success: false, message: "Coupon creation failed" };
      }

      revalidateTag("coupons");

      if (data?.id) {
        return { success: true, message: "Coupon copied" };
      }
      return { success: true, message: "Coupon created" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Coupon already exists");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}

export async function getCouponAction(id: number) {
  try {
    const coupon = await getCoupon(id);

    if (!coupon) {
      return { success: false, message: "Coupon not found", data: {} };
    }

    return { success: true, message: "Coupon Found", data: coupon };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function deleteCouponAction(id: number | null) {
  try {
    if (!id) {
      return { success: false, message: "Coupon ID is required" };
    }

    const result = await deleteCoupon(id);

    if (!result) {
      return { success: false, message: "Coupon not found" };
    }

    revalidateTag("coupons");
    return { success: true, message: "Coupon deleted" };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function updateCouponAction(id: number, data: IUpdateCoupon) {
  try {
    if (!id) {
      return { success: false, message: "Coupon ID is required" };
    }
    const validated = UpdateCouponSchema.safeParse(data);

    if (validated.success) {
      await updateCoupon(id, validated.data);
      revalidateTag("coupons");
      return { success: true, message: "Coupon updated" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Coupon already exists");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}
