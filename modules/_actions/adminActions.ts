"use server";

import { addAdmin, deleteAdmin, getAdmin, updateAdmin, updatePassword, updatePermission } from "@/lib/admin.lib";
import { getUser, updateSession } from "@/lib/session";
import { TAddAdmin, TAdminPassword, TUpdateAdmin } from "@/types/admin";
import { exclude, getFirstZodError, handlePrismaError } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";
import bcrypt from "bcrypt";
import { revalidateTag } from "next/cache";
import { AddAdminSchema, UpdateAdminSchema, UpdatePasswordSchema } from "../validations/admins";

export async function addAdminAction(data: TAddAdmin) {
  try {
    const validated = AddAdminSchema.safeParse(data);

    if (validated.success) {
      const { name, email, password, type, scopes } = validated.data;
      const hashedPassword = await bcrypt.hash(password, 10);

      const admin = await addAdmin(name, email, hashedPassword, type, scopes);

      if (!admin) {
        return { success: false, message: "Admin creation failed" };
      }

      revalidateTag("admins");
      return { success: true, message: "Admin created" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Admin already exists");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}

export async function deleteAdminAction(id: number | null) {
  try {
    if (!id) {
      return { success: false, message: "Admin ID is required" };
    }

    const result = await deleteAdmin(id);

    if (!result) {
      return { success: false, message: "Admin not found" };
    }

    revalidateTag("admins");
    return { success: true, message: "Admin deleted" };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function getAdminAction(id: number) {
  try {
    const admin = await getAdmin(id);

    if (!admin) {
      return { success: false, message: "Admin not found", data: {} };
    }

    return { success: true, message: "Admin Found", data: exclude(admin, ["password"]) };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function updateAdminAction(id: number | null, data: TUpdateAdmin) {
  try {
    if (!id) {
      return { success: false, message: "Admin ID is required" };
    }
    const validated = UpdateAdminSchema.safeParse(data);

    if (validated.success) {
      const { name, email, type } = validated.data;

      const currentUser = await getUser();

      const updatedAdmin = await updateAdmin(id, name, email, type);

      if (!updatedAdmin) {
        return { success: false, message: "Admin updated failed" };
      }

      if (id === currentUser?.id) {
        await updateSession(updatedAdmin?.email, JSON.stringify(updatedAdmin?.scopes));
      }
      revalidateTag("admins");
      return { success: true, message: "Admin updated" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError) {
      return handlePrismaError(err, "Admin already exists");
    }
    return { success: false, message: "An unexpected error occurred" };
  }
}

export async function updatePasswordAction(id: number | null, data: TAdminPassword) {
  try {
    if (!id) {
      return { success: false, message: "Admin ID is required" };
    }
    const validated = UpdatePasswordSchema.safeParse(data);

    if (validated.success) {
      const { password } = data;

      const hashedPassword = await bcrypt.hash(password, 10);

      const updatedPassword = await updatePassword(id, hashedPassword);

      if (!updatedPassword) {
        return { success: false, message: "Password update failed" };
      }

      revalidateTag("admins");
      return { success: true, message: "Password updated" };
    } else {
      return { success: false, message: getFirstZodError(validated.error) };
    }
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}

export async function updatePermissionAction(id: number | null, data: string[]) {
  try {
    if (!id) {
      return { success: false, message: "Admin ID is required" };
    }

    const currentUser = await getUser();

    const updatedAdmin = await updatePermission(id, data);

    if (!updatedAdmin) {
      return { success: false, message: "Permission Update failed" };
    }
    if (id === currentUser?.id) {
      await updateSession(updatedAdmin?.email, JSON.stringify(updatedAdmin?.scopes));
    }
    revalidateTag("admins");
    return { success: true, message: "Permissions updated" };
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error("An unexpected error occurred");
  }
}
