/* eslint-disable react-hooks/exhaustive-deps */

"use client";

import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import { CheckIcon, ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { type FC, JSX, useEffect, useRef, useState } from "react";
import { Button } from "../button";
import { Calendar } from "../calendar";
import { Label } from "../label";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../select";
import { Switch } from "../switch";
import { DateInput } from "./date-input";

export interface DateRangePickerProps {
  /** Click handler for applying the updates from DateRangePicker. */
  // eslint-disable-next-line no-unused-vars
  onUpdate?: (values: { range: DateRange; rangeCompare?: DateRange }) => void;
  /** Initial value for start date */
  initialDateFrom?: Date | string;
  /** Initial value for end date */
  initialDateTo?: Date | string;
  /** Initial value for start date for compare */
  initialCompareFrom?: Date | string;
  /** Initial value for end date for compare */
  initialCompareTo?: Date | string;
  /** Alignment of popover */
  align?: "start" | "center" | "end";
  /** Option for locale */
  locale?: string;
  /** Option for showing compare feature */
  showCompare?: boolean;
}

const formatDate = (date: Date, locale: string = "en-us"): string => {
  return date.toLocaleDateString(locale, {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

interface DateRange {
  from?: Date;
  to?: Date | undefined;
}

interface Preset {
  name: string;
  label: string;
}

// Define presets
const PRESETS: Preset[] = [
  { name: "today", label: "Today" },
  { name: "yesterday", label: "Yesterday" },
  { name: "last7", label: "Last 7 days" },
  { name: "last30", label: "Last 30 days" },
  { name: "thisWeek", label: "This Week" },
  { name: "lastWeek", label: "Last Week" },
  { name: "thisMonth", label: "This Month" },
  { name: "lastMonth", label: "Last Month" },
  { name: "thisYear", label: "This Year" },
  { name: "lastYear", label: "Last Year" },
];

/** The DateRangePicker component allows a user to select a range of dates */
export const DateRangePicker: FC<DateRangePickerProps> & {
  filePath: string;
} = ({
  initialDateFrom = new Date(new Date().setHours(0, 0, 0, 0)),
  initialDateTo,
  initialCompareFrom,
  initialCompareTo,
  onUpdate,
  align = "end",
  locale = "en-US",
  showCompare = true,
}): JSX.Element => {
  const [isOpen, setIsOpen] = useState(false);

  const searchParams = useSearchParams();
  const start = searchParams.get("startDate") || initialDateFrom;
  const end = searchParams.get("endDate") || initialDateTo;

  const [range, setRange] = useState<DateRange>({
    from: new Date(new Date(initialDateFrom).setHours(0, 0, 0, 0)),
    to: initialDateTo
      ? new Date(new Date(initialDateTo).setHours(0, 0, 0, 0))
      : new Date(new Date(initialDateFrom).setHours(0, 0, 0, 0)),
  });
  const [rangeCompare, setRangeCompare] = useState<DateRange | undefined>(
    initialCompareFrom
      ? {
          from: new Date(new Date(initialCompareFrom).setHours(0, 0, 0, 0)),
          to: initialCompareTo
            ? new Date(new Date(initialCompareTo).setHours(0, 0, 0, 0))
            : new Date(new Date(initialCompareFrom).setHours(0, 0, 0, 0)),
        }
      : undefined
  );

  // Refs to store the values of range and rangeCompare when the date picker is opened
  const openedRangeRef = useRef<DateRange | null>(null);
  const openedRangeCompareRef = useRef<DateRange | undefined>(null);

  const [selectedPreset, setSelectedPreset] = useState<string | undefined>(undefined);

  const [isSmallScreen, setIsSmallScreen] = useState(typeof window !== "undefined" ? window.innerWidth < 960 : false);

  const startDate = dayjs(start).format("DD MMM, YYYY");
  const endDate = dayjs(end).format("DD MMM, YYYY");
  const interval = dayjs(endDate).diff(startDate, "days");

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const handleResize = (): void => {
      setIsSmallScreen(window.innerWidth < 960);
    };

    window.addEventListener("resize", handleResize);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true);
      return;
    }

    const startDateObj = start ? dayjs(start).toDate() : undefined;
    const endDateObj = end ? dayjs(end).toDate() : undefined;

    for (const preset of PRESETS) {
      const presetRange = getPresetRange(preset.label);

      const normalizedStartDate = new Date(startDateObj?.setHours(0, 0, 0, 0) ?? 0);
      const normalizedPresetFrom = new Date(presetRange.from?.setHours(0, 0, 0, 0) ?? 0);

      const normalizedEndDate = new Date(endDateObj?.setHours(0, 0, 0, 0) ?? 0);
      const normalizedPresetTo = new Date(presetRange.to?.setHours(0, 0, 0, 0) ?? 0);

      if (
        normalizedStartDate.getTime() === normalizedPresetFrom.getTime() &&
        normalizedEndDate.getTime() === normalizedPresetTo.getTime()
      ) {
        setSelectedPreset(preset.label);
        return;
      }
    }
    setSelectedPreset(undefined);
  }, [start, end, isInitialized]);

  const checkPresetValue = selectedPreset ? selectedPreset : interval > 0 ? `${startDate} - ${endDate}` : startDate;

  const getPresetRange = (presetLabel: string): DateRange => {
    const preset = PRESETS.find(({ label }) => label === presetLabel);
    if (!preset) throw new Error(`Unknown date range preset: ${presetLabel}`);
    const from = new Date();
    const to = new Date();
    const first = from.getDate() - from.getDay();

    switch (preset?.label) {
      case "Today":
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "Yesterday":
        from.setDate(from.getDate() - 1);
        from.setHours(0, 0, 0, 0);
        to.setDate(to.getDate() - 1);
        to.setHours(23, 59, 59, 999);
        break;
      case "Last 7 days":
        from.setDate(from.getDate() - 6);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "Last 30 days":
        from.setDate(from.getDate() - 29);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "This Week":
        from.setDate(first);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "Last Week":
        from.setDate(from.getDate() - 7 - from.getDay());
        to.setDate(to.getDate() - to.getDay() - 1);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "This Month":
        from.setDate(1);
        from.setHours(0, 0, 0, 0);
        to.setHours(23, 59, 59, 999);
        break;
      case "Last Month":
        from.setMonth(from.getMonth() - 1);
        from.setDate(1);
        from.setHours(0, 0, 0, 0);
        to.setDate(0);
        to.setHours(23, 59, 59, 999);
        break;
      case "This Year":
        from.setMonth(0, 1);
        from.setHours(0, 0, 0, 0);
        to.setMonth(11, 31);
        to.setHours(23, 59, 59, 999);
        break;
      case "Last Year":
        from.setFullYear(from.getFullYear() - 1, 0, 1);
        from.setHours(0, 0, 0, 0);
        to.setFullYear(to.getFullYear() - 1, 11, 31);
        to.setHours(23, 59, 59, 999);
        break;
    }

    return { from, to };
  };

  const setPreset = (preset: string): void => {
    const range = getPresetRange(preset);
    setRange(range);
    if (rangeCompare) {
      const rangeCompare = {
        from: new Date(
          range?.from?.getFullYear() ?? new Date().getFullYear() - 1,
          range?.from?.getMonth() ?? new Date().getMonth(),
          range?.from?.getDate()
        ),
        to: range?.to ? new Date(range?.to.getFullYear() - 1, range?.to.getMonth(), range?.to.getDate()) : undefined,
      };
      setRangeCompare(rangeCompare);
    }
  };

  const checkPreset = (): void => {
    for (const preset of PRESETS) {
      const presetRange = getPresetRange(preset.label);

      const normalizedRangeFrom = new Date(range?.from?.setHours(0, 0, 0, 0) ?? 0);
      const normalizedPresetFrom = new Date(presetRange?.from?.setHours(0, 0, 0, 0) ?? 0);

      const normalizedRangeTo = new Date(range?.to?.setHours(0, 0, 0, 0) ?? 0);
      const normalizedPresetTo = new Date(presetRange?.to?.setHours(0, 0, 0, 0) ?? 0);

      if (
        normalizedRangeFrom.getTime() === normalizedPresetFrom.getTime() &&
        normalizedRangeTo.getTime() === normalizedPresetTo.getTime()
      ) {
        setSelectedPreset(preset.label);
        return;
      }
    }
    setSelectedPreset(undefined);
  };

  const resetValues = (): void => {
    setRange({
      from: typeof initialDateFrom === "string" ? new Date(initialDateFrom) : initialDateFrom,
      to: initialDateTo
        ? typeof initialDateTo === "string"
          ? new Date(initialDateTo)
          : initialDateTo
        : typeof initialDateFrom === "string"
          ? new Date(initialDateFrom)
          : initialDateFrom,
    });
    setRangeCompare(
      initialCompareFrom
        ? {
            from: typeof initialCompareFrom === "string" ? new Date(initialCompareFrom) : initialCompareFrom,
            to: initialCompareTo
              ? typeof initialCompareTo === "string"
                ? new Date(initialCompareTo)
                : initialCompareTo
              : typeof initialCompareFrom === "string"
                ? new Date(initialCompareFrom)
                : initialCompareFrom,
          }
        : undefined
    );
  };

  useEffect(() => {
    checkPreset();
  }, [range]);

  const PresetButton = ({ label, isSelected }: { preset: string; label: string; isSelected: boolean }): JSX.Element => (
    <Button
      className={cn(isSelected && "pointer-events-none")}
      variant="ghost"
      onClick={() => {
        setPreset(label);
      }}
    >
      <>
        <span className={cn("pr-2 opacity-0", isSelected && "opacity-70")}>
          <CheckIcon
            width={18}
            height={18}
          />
        </span>
        {label}
      </>
    </Button>
  );

  // Helper function to check if two date ranges are equal
  const areRangesEqual = (a?: DateRange, b?: DateRange) => {
    if (!a || !b) return a === b; // If either is undefined, return true if both are undefined
    return a?.from?.getTime() === b?.from?.getTime() && (!a.to || !b.to || a?.to?.getTime() === b?.to?.getTime());
  };

  useEffect(() => {
    if (isOpen) {
      openedRangeRef.current = range;
      openedRangeCompareRef.current = rangeCompare;
    }
  }, [isOpen]);

  return (
    <Popover
      modal={true}
      open={isOpen}
      onOpenChange={(open: boolean) => {
        if (!open) {
          resetValues();
        }
        setIsOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          size="md"
          variant="outline"
        >
          <div className="flex items-center justify-center">
            <div>
              {start && end ? "Date: " + checkPresetValue : <h2 className="text-[12px]">All Time</h2>}

              {rangeCompare != null && (
                <div className="opacity-60 text-xs -mt-1">
                  <>
                    vs. {formatDate(rangeCompare?.from as Date, locale)}
                    {rangeCompare.to != null ? ` - ${formatDate(rangeCompare.to, locale)}` : ""}
                  </>
                </div>
              )}
            </div>

            <div className="pl-1 opacity-60 mr-2 scale-125">
              {isOpen ? <ChevronUpIcon size="14px" /> : <ChevronDownIcon size="14px" />}
            </div>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align={align}
        className="w-auto"
        updatePositionStrategy="always"
      >
        <div className="flex py-2">
          <div className="flex">
            <div className="flex flex-col">
              <div className="flex flex-col lg:flex-row gap-2 px-3 justify-end items-center lg:items-start pb-4 lg:pb-0">
                <div className="flex items-center space-x-2 pr-4 py-1">
                  {showCompare && (
                    <>
                      <Switch
                        defaultChecked={Boolean(rangeCompare)}
                        onCheckedChange={(checked: boolean) => {
                          if (checked) {
                            if (!range?.to) {
                              setRange({
                                from: range?.from,
                                to: range?.from,
                              });
                            }
                            setRangeCompare({
                              from: new Date(
                                range?.from?.getFullYear() ?? new Date().getFullYear(),
                                range?.from?.getMonth() ?? new Date().getMonth(),
                                range?.from?.getDate() ?? new Date().getDate() - 365
                              ),
                              to: range?.to
                                ? new Date(range?.to.getFullYear() - 1, range?.to.getMonth(), range?.to.getDate())
                                : new Date(
                                    range?.from?.getFullYear() ?? new Date().getFullYear() - 1,
                                    range?.from?.getMonth() ?? new Date().getMonth(),
                                    range?.from?.getDate() ?? new Date().getDate()
                                  ),
                            });
                          } else {
                            setRangeCompare(undefined);
                          }
                        }}
                        id="compare-mode"
                      />
                      <Label htmlFor="compare-mode">Compare</Label>
                    </>
                  )}
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex gap-2">
                    <DateInput
                      value={range?.from}
                      onChange={(date) => {
                        const toDate = range?.to == null || date > range?.to ? date : range?.to;
                        setRange((prevRange) => ({
                          ...prevRange,
                          from: date,
                          to: toDate,
                        }));
                      }}
                    />
                    <div className="py-1">-</div>
                    <DateInput
                      value={range?.to}
                      onChange={(date) => {
                        const fromDate = date < (range?.from ?? date) ? date : (range?.from ?? date);
                        setRange((prevRange) => ({
                          ...prevRange,
                          from: fromDate,
                          to: date,
                        }));
                      }}
                    />
                  </div>
                  {rangeCompare != null && (
                    <div className="flex gap-2">
                      <DateInput
                        value={rangeCompare?.from}
                        onChange={(date) => {
                          if (rangeCompare) {
                            const compareToDate =
                              rangeCompare.to == null || date > rangeCompare.to ? date : rangeCompare.to;
                            setRangeCompare((prevRangeCompare) => ({
                              ...prevRangeCompare,
                              from: date,
                              to: compareToDate,
                            }));
                          } else {
                            setRangeCompare({
                              from: date,
                              to: new Date(),
                            });
                          }
                        }}
                      />
                      <div className="py-1">-</div>
                      <DateInput
                        value={rangeCompare?.to}
                        onChange={(date) => {
                          if (rangeCompare && rangeCompare.from) {
                            const compareFromDate = date < rangeCompare.from ? date : rangeCompare.from;
                            setRangeCompare({
                              ...rangeCompare,
                              from: compareFromDate,
                              to: date,
                            });
                          }
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
              {isSmallScreen && (
                <Select
                  defaultValue={selectedPreset}
                  onValueChange={(value) => {
                    setPreset(value);
                  }}
                >
                  <SelectTrigger className="w-[180px] mx-auto mb-2">
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {PRESETS.map((preset) => (
                      <SelectItem
                        key={preset.label}
                        value={preset.label}
                      >
                        {preset.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              <div>
                <Calendar
                  initialFocus
                  mode="range"
                  onSelect={(value) => {
                    if (value?.from && !value?.to) {
                      setRange({
                        from: value?.from,
                        to: value?.from,
                      });
                    } else {
                      setRange(value as DateRange);
                    }
                  }}
                  selected={range as any}
                  numberOfMonths={isSmallScreen ? 1 : 2}
                  defaultMonth={new Date(new Date().setMonth(new Date().getMonth() - (isSmallScreen ? 0 : 1)))}
                />
              </div>
            </div>
          </div>
          {!isSmallScreen && (
            <div className="flex flex-col items-end gap-1 pr-2 max-h-[22rem] overflow-y-auto">
              {PRESETS.map((preset) => (
                <PresetButton
                  key={preset.name}
                  preset={preset.name}
                  label={preset.label}
                  isSelected={selectedPreset === preset.label}
                />
              ))}
            </div>
          )}
        </div>
        <div className="flex justify-end gap-2 py-2 pr-4">
          <Button
            size="sm"
            onClick={() => {
              setIsOpen(false);
              resetValues();
            }}
            variant="ghost"
          >
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={() => {
              setIsOpen(false);
              if (
                !areRangesEqual(range, openedRangeRef.current as DateRange) ||
                !areRangesEqual(rangeCompare, openedRangeCompareRef.current as DateRange)
              ) {
                onUpdate?.({ range, rangeCompare });
              }
            }}
          >
            Apply
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

DateRangePicker.displayName = "DateRangePicker";
DateRangePicker.filePath = "libs/shared/ui-kit/src/lib/date-range-picker/date-range-picker.tsx";
