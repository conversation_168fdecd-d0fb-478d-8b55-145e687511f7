import React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const badgeVariants = cva("inline-flex items-center", {
  variants: {
    variant: {
      default: "bg-gray-100 text-gray-600",
      danger: "bg-red-100 text-red-700",
      warning: "bg-yellow-100 text-yellow-800",
      info: "bg-blue-100 text-blue-700",
      success: "bg-green-100 text-green-700",
    },
    size: {
      regular: "text-xs font-medium px-2 py-1 rounded-full",
      sm: "text-[0.7em] font-normal px-4 py-[0.1rem] rounded-xl",
      xsm: "text-[0.62rem] font-normal px-2 py-[0.02rem] rounded-xl",
    },
    textTransform: {
      capital: "capitalize",
      upper: "uppercase",
      lower: "lowercase",
      regular: "",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "regular",
    textTransform: "regular",
  },
});
export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement>, VariantProps<typeof badgeVariants> {
  asChild?: boolean;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant, size, textTransform, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "span";
    return (
      <Comp
        className={cn(badgeVariants({ variant, className, size, textTransform }))}
        ref={ref}
        {...props}
      />
    );
  }
);

Badge.displayName = "Badge";

export { Badge, badgeVariants };
