import { Button } from "@/components/ui/button";
import AdminCardAction from "@/modules/components/admins/AdminCardAction";
import { Variant } from "@/modules/hooks/useVariant";
import { ReactEventHandler } from "react";

export type TableIconAction = {
  title: string;
  url?: string;
  Icon?: React.FC<{
    className?: string;
  }>;
  onAction?: ReactEventHandler;
  variant?: Variant;
};

// eslint-disable-next-line no-unused-vars
type TableIconActions = {
  actions: TableIconAction[];
};

export default function TableIconActions({ actions }: TableIconActions) {
  return (
    <div className="flex gap-1 justify-center">
      {actions?.map((action, idx) => (
        <div
          className="flex"
          key={idx}
        >
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8"
            onClick={action.onAction}
          >
            {action.Icon && (
              <AdminCardAction
                Icon={action.Icon}
                onAction={action.onAction}
                tooltipText={action.title}
                variant={action.variant}
                url={action.url}
              />
            )}
          </Button>
        </div>
      ))}
    </div>
  );
}
