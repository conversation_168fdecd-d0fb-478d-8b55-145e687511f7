import { logoutAction } from "@/modules/_actions/logoutAction";
import { useAuth } from "@/modules/hooks/useAuth";
import { INavBarWithChild } from "@/types/common";
import classNames from "@/utlis/helpers";
import { ChevronDown, PowerOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import TooltipWrapper from "./TooltipWrapper";

const mountedStyle = {
  animation: "sidebarTransitionIn 300ms ease-in",
};
const unmountedStyle = {
  animation: "sidebarTransitionOut 300ms ease-out",
  animationFillMode: "forwards",
};

export default function SidebarMenu({ nav }: { nav: INavBarWithChild[] }) {
  const [openNav, setOpenNav] = useState<string | null>(null);

  const { user, setUser } = useAuth();
  const router = useRouter();

  const userScopes = user?.scopes ? JSON.parse(user.scopes as string) : "[]";

  const hasScope = (requiredScopes: string[]) => {
    return requiredScopes.some((scope) => userScopes.includes(scope as string));
  };

  const toggleChildNav = (itemName: string) => {
    setOpenNav((prev) => (prev === itemName ? null : itemName));
  };

  const handleSignOut = async () => {
    try {
      await logoutAction();
      setUser(null);
      router.push("/login");
    } catch (error) {
      return null;
    }
  };

  const renderLink = (item: any, isEnabled: boolean) => (
    <Link
      href={isEnabled ? item.href : "#"}
      onClick={(e) => {
        if (item.child) {
          e.preventDefault();
          toggleChildNav(item.name);
        }
      }}
      className={classNames(
        isEnabled
          ? item.current
            ? "bg-gray-800 text-white"
            : "text-gray-400 hover:text-white hover:bg-gray-800"
          : "text-gray-600 cursor-not-allowed",
        "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold items-center justify-between"
      )}
    >
      {
        item.icon && (
          <item.icon
            className="h-6 w-6 shrink-0"
            aria-hidden="true"
          />
        )
        // Default icon
      }
      <span className="flex-1">{item.name}</span>
      {item.child && (
        <ChevronDown className={classNames("transition-all", openNav === item.name ? "rotate-180" : "rotate-0")} />
      )}
    </Link>
  );

  return (
    <>
      <ul
        role="list"
        className="flex flex-1 flex-col gap-y-7 justify-between"
      >
        <li>
          <ul
            role="list"
            className="-mx-2 space-y-1"
          >
            {nav?.map((item) => {
              const isAdminMenu = item?.name === "Settings";
              const itemEnabled = isAdminMenu || hasScope(item?.requiredScopes || []);
              return (
                <li key={item.name}>
                  {itemEnabled ? (
                    renderLink(item, itemEnabled)
                  ) : (
                    <TooltipWrapper tooltipText="Insufficient Permission">
                      {renderLink(item, itemEnabled)}
                    </TooltipWrapper>
                  )}

                  {item?.child && openNav === item?.name && (
                    <ul
                      className="ml-9"
                      style={openNav === item.name ? mountedStyle : unmountedStyle}
                    >
                      {item?.child?.map((childItem) => {
                        const isChildAdminMenu = childItem?.name === "Admins";

                        const childItemEnabled = isChildAdminMenu || hasScope(childItem.requiredScopes || []);
                        return (
                          <li key={childItem.name}>
                            {childItemEnabled ? (
                              renderLink(childItem, childItemEnabled)
                            ) : (
                              <TooltipWrapper tooltipText="Insufficient Permission">
                                {renderLink(childItem, childItemEnabled)}
                              </TooltipWrapper>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>
        </li>

        <li>
          <Link
            href="#"
            onClick={handleSignOut}
            className={classNames(
              "text-gray-400 hover:text-white hover:bg-gray-800",
              "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold items-center"
            )}
          >
            <PowerOff className="h-6 w-6" />
            Sign Out
          </Link>
        </li>
      </ul>
    </>
  );
}
