"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useAuth } from "@/modules/hooks/useAuth";
import { useGravater } from "@/modules/hooks/useGravatar";
import { ChevronDownIcon } from "lucide-react";
import Image from "next/image";

export default function ProfileImage() {
  const { user } = useAuth();

  const avaterUrl = useGravater(user?.email as string);
  return (
    <>
      <DropdownMenuTrigger asChild>
        <Button
          variant="link"
          className="hover:no-underline focus-visible:ring-0"
        >
          <Image
            className="h-10 w-10 rounded-full p-1 bg-gray-100"
            src={avaterUrl}
            alt="profile pic"
            height={20}
            width={20}
          />
          <span className="hidden lg:flex lg:items-center">
            <span
              className="ml-4 text-sm font-semibold leading-6 text-gray-900"
              aria-hidden="true"
            >
              {user?.name ? user?.name : <dt className="h-2 bg-slate-300 rounded w-16"></dt>}
            </span>
            <ChevronDownIcon
              className="ml-2 h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </span>
        </Button>
      </DropdownMenuTrigger>
    </>
  );
}
