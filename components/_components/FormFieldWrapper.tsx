// import React from "react";
// import { Controller } from "react-hook-form";

// export const FormFieldWrapper = ({ label, required = false, name, control, rules, error, children }: any) => {
//   return (
//     <div>
//       {label && (
//         <label className="text-[13px] text-gray-500 font-normal mb-1 inline-block">
//           {label} {required && <span className="text-red-500">*</span>}
//         </label>
//       )}
//       <Controller
//         name={name}
//         control={control}
//         rules={rules}
//         render={({ field }) => {
//           return React.cloneElement(children, {
//             ...children.props,
//             field,
//           });
//         }}
//       />
//       {error && <p className="text-red-500 text-xs mt-1 ml-1">{error}</p>}
//     </div>
//   );
// };

// export default FormFieldWrapper;

import React from "react";
import { Control, Controller, FieldPath, FieldValues } from "react-hook-form";

interface IFieldWrapper<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label?: string;
  required?: boolean;
  error?: string;
  rules?: any; // validation rules
  children?: React.ReactNode; // You can still pass custom components if needed
}

const FormFieldWrapper = <T extends FieldValues>({
  name,
  control,
  label,
  required = false,
  error,
  rules,
  children,
}: IFieldWrapper<T>) => {
  return (
    <div>
      {label && (
        <label className="text-[13px] text-gray-500 font-normal mb-1 inline-block">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      {/* Controller part */}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <>{children ? React.cloneElement(children as React.ReactElement, { ...field }) : <input {...field} />}</>
        )}
      />
      {error && <p className="text-red-500 text-xs mt-1 ml-1">{error}</p>}
    </div>
  );
};

export default FormFieldWrapper;
