import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";

interface IAccordionWrapper {
  title: string;
  children: React.ReactNode;
  background?: string;
  defaultValue?: string;
  margin?: "mx-0" | "mx-1" | "mx-2" | "mx-3";
}

export default function AccordionWrapper({
  title,
  children,
  background = "bg-white",
  defaultValue = "",
  margin = "mx-0",
}: IAccordionWrapper) {
  return (
    <Accordion
      type="single"
      collapsible
      className="w-full"
      defaultValue={defaultValue}
    >
      <AccordionItem
        value="item-1"
        className={cn(
          `grid grid-cols-1 divide-y divide-gray-200 overflow-hidden ${margin} rounded-lg shadow`,
          background
        )}
      >
        <AccordionTrigger className="py-2 px-4 text-sm font-medium leading-6 text-gray-900 hover:no-underline text-left">
          {title}
        </AccordionTrigger>
        <AccordionContent className="">{children ? children : "No Data Found"}</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
