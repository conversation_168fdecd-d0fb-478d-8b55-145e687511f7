interface DataItem {
  key: string;
  value: string | string[] | boolean | null;
}

interface AccordionContentListProps {
  data: DataItem[];
}

export default function AccordionContentList({ data }: AccordionContentListProps) {
  if (data) {
    return (
      <div className="px-4 py-3 pb-0">
        {data?.map(({ key, value }) => (
          <div
            className="py-1 flex gap-6"
            key={key}
          >
            <p className="text-sm font-medium leading-6 text-gray-900 basis-[150px] min-w-[150px]">{key}</p>
            <div className="flex-1 break-all">
              <span className={key === "ID" ? "break-all" : ""}>{value?.toString()}</span>
            </div>
          </div>
        ))}
      </div>
    );
  } else {
    return <div className="p-4 pb-0">No Data Found</div>;
  }
}
