import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CommandList } from "cmdk";
import { CheckIcon, ChevronsUpDown } from "lucide-react";
import { useState } from "react";

type ComboBoxItemProps = {
  label: string;
  value: string;
};

type ComboBoxWrapperProps = {
  title: string;
  items: ComboBoxItemProps[];
  value?: string;
  onSelect?: (value: string) => void;
  searchOption?: boolean;
  inputWidth?: string;
  popoverWidth?: string;
};

export default function ComboBoxWrapper({
  title,
  searchOption = false,
  items,
  onSelect,
  popoverWidth = "120px",
  value,
}: ComboBoxWrapperProps) {
  const [open, setOpen] = useState(false);
  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between h-8 text-xs p-2")}
        >
          {value ? title + ": " + items?.find((item) => item?.value == value)?.label : title}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn("p-0", `w-[${popoverWidth}]`)}
        align="start"
      >
        <Command>
          {searchOption && (
            <CommandInput
              placeholder="Search..."
              className="h-7"
            />
          )}
          <CommandList className="max-h-96 overflow-auto">
            <CommandEmpty>No items found.</CommandEmpty>
            <CommandGroup>
              {items?.length > 0 ? (
                items?.map((item, idx) => (
                  <CommandItem
                    key={idx}
                    value={item?.label}
                    onSelect={(currentLabel) => {
                      const foundItem = items?.find((item) => item.label === currentLabel);
                      const value = foundItem ? foundItem.value : "";
                      setOpen(false);
                      onSelect && onSelect(value);
                    }}
                  >
                    {item.label}
                    <CheckIcon
                      className={cn(
                        "ml-auto h-4 w-4",
                        String(item.value) === String(value) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))
              ) : (
                <CommandEmpty>No items found.</CommandEmpty>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
