"use client";

import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import { Description } from "@radix-ui/react-dialog";
import { useRouter } from "next/navigation";
import { ReactNode, useCallback, useState } from "react";

type SlideOverProps = {
  open: boolean;
  title: string;
  children?: ReactNode;
};

export default function SlideOver({ open, title, children }: SlideOverProps) {
  const router = useRouter();
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = useCallback(
    (isOpen: boolean) => {
      if (!isOpen) {
        setIsClosing(true);
        setTimeout(() => {
          router.back();
        }, 500);
      }
    },
    [router]
  );

  return (
    <Sheet
      open={open && !isClosing}
      onOpenChange={handleClose}
    >
      <SheetContent className="w-11/12 sm:max-w-[575px] p-4">
        <SheetTitle className="sticky top-0 pb-2">{title}</SheetTitle>
        <Description />
        <div
          style={{ height: "calc(100vh - 60px)" }}
          className="overflow-auto"
        >
          <div className="py-2">{children}</div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
