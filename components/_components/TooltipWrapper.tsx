import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function TooltipWrapper({
  tooltipText,
  children,
  textSize = "sm",
}: {
  tooltipText: string | null;
  children?: React.ReactNode;
  textSize?: string;
}) {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger
          asChild
          className="cursor-pointer"
        >
          <div
          // className={cn(
          //   "relative -mr-px inline-flex w-fit flex-1 items-center justify-center gap-x-3 font-semibold text-gray-900 cursor-pointer"
          // )}
          >
            {children}
          </div>
        </TooltipTrigger>
        {tooltipText && (
          <TooltipContent>
            <p className={`text-${textSize} font-medium`}>{tooltipText}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}
