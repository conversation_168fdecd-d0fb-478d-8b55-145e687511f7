"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useVariant, Variant } from "@/modules/hooks/useVariant";
import classNames from "@/utlis/helpers";
import { EllipsisIcon } from "lucide-react";
import Link from "next/link";
import TooltipWrapper from "./TooltipWrapper";

export type TTableDropdownAction = {
  title: string;
  url?: string;
  Icon?: React.FC<{
    className?: string;
  }>;
  onAction?: React.ReactEventHandler;
  variant?: Variant;
  disabled?: boolean;
  tooltip?: string;
};
export type TTableDropdownActionSeperator = {
  type: "seperator";
};

export type TTableDropdownActionOrSeperator = TTableDropdownAction | TTableDropdownActionSeperator;

type TTableDropdownActions = {
  label: string;
  domain?: string;
  actions?: any;
};

function isSeparator(action: TTableDropdownActionOrSeperator): action is TTableDropdownActionSeperator {
  return (action as TTableDropdownActionSeperator).type === "seperator";
}

export function TableDropdownActions({ label, domain, actions }: TTableDropdownActions) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="secondary"
          size="icon"
          className="h-8 w-8"
        >
          <EllipsisIcon size="16px" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-52"
        align="end"
      >
        {label && (
          <>
            <DropdownMenuLabel>{label}</DropdownMenuLabel>
            <DropdownMenuSeparator />
          </>
        )}
        <DropdownMenuGroup>
          {actions?.map((action: TTableDropdownActionOrSeperator, idx: number) =>
            isSeparator(action) ? (
              <DropdownMenuSeparator key={idx} />
            ) : (
              <CDropdownMenuItem
                key={idx}
                {...action}
              />
            )
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function CDropdownMenuItem({ title, Icon, url, onAction, variant, disabled, tooltip }: TTableDropdownAction) {
  const { textColor, bgColor } = useVariant(variant);

  const content = (
    <div
      className={classNames(
        "cursor-pointer",
        "font-medium",
        "items-start",
        textColor,
        `hover:${bgColor}`,
        `hover:${textColor}`,
        `focus:${textColor}`,
        disabled ? "opacity-50 cursor-not-allowed" : ""
      )}
    >
      <div className="flex">
        {Icon && <Icon className="mr-2 h-1 mt-0.5" />}
        <span>{title}</span>
      </div>
    </div>
  );

  return (
    <TooltipWrapper tooltipText={tooltip as string}>
      {url ? (
        <DropdownMenuItem asChild>
          <Link href={url}>{content}</Link>
        </DropdownMenuItem>
      ) : onAction ? (
        <DropdownMenuItem disabled={disabled} onClick={onAction}>
          {content}
        </DropdownMenuItem>
      ) : (
        <DropdownMenuItem disabled={disabled}>{content}</DropdownMenuItem>
      )}
    </TooltipWrapper>
  );
}
