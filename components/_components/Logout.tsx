"use client";

import { logoutAction } from "@/modules/_actions/logoutAction";
import { useAuth } from "@/modules/hooks/useAuth";
import { useRouter } from "next/navigation";

export default function Logout() {
  const router = useRouter();
  // const pathname = usePathname();
  const { setUser } = useAuth();

  const handleSignOut = async () => {
    try {
      await logoutAction();
      setUser(null);
      router.push("/login");
    } catch (error) {
      return null;
      // console.error("Logout failed:", error);
    }
  };

  return (
    <button
      className="text-sm font-medium"
      type="button"
      onClick={handleSignOut}
    >
      Sign Out
    </button>
  );
}
