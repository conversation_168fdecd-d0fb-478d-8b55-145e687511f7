import { DropdownMenu, DropdownMenuContent } from "@/components/ui/dropdown-menu";
import { BellIcon, PowerOff, SearchIcon } from "lucide-react";
import Logout from "./Logout";
import ProfileImage from "./ProfileImage";
import TopbarMenu from "./TopbarMenu";

export default function Topbar() {
  return (
    <div className="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8 topbar">
      <TopbarMenu />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        <form
          className="relative flex flex-1"
          action="#"
          method="GET"
        >
          <SearchIcon className="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400" />
          <input
            id="search-field"
            className="block h-full w-full border-0 py-0 pl-8 pr-0 text-gray-900 placeholder:text-gray-400 sm:text-sm"
            placeholder="Search..."
            type="search"
            name="search"
          />
        </form>
        <div className="flex items-center gap-x-4 lg:gap-x-6">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
          >
            <BellIcon
              className="h-6 w-6"
              aria-hidden="true"
            />
          </button>

          <div
            className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10"
            aria-hidden="true"
          />

          <DropdownMenu>
            <ProfileImage />
            <DropdownMenuContent
              className="w-[150px]"
              align="end"
            >
              {/* <DropdownMenuGroup>
                {userNavigation.map((item) => (
                  <DropdownMenuItem key={item.name}>
                    <a href={item.href}>{item.name}</a>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup> */}
              {/* {userNavigation.length > 0 && <DropdownMenuSeparator />} */}

              <div className="flex items-center cursor-pointer gap-3 px-3 py-1">
                <PowerOff size="16px" />
                <Logout />
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
