"use client";

import { useSidebar } from "@/modules/hooks/useSidebar";
import { scopes } from "@/modules/mappers/adminRolesPermission";
import { INavBarWithChild } from "@/types/common";
import { Dialog, Transition } from "@headlessui/react";
import { Home, Settings, Store, X } from "lucide-react";
import { usePathname } from "next/navigation";
import { Fragment, useCallback, useEffect, useState } from "react";
import AppLogo from "./AppLogo";
import SidebarMenu from "./SidebarMenu";

export default function Sidebar() {
  const pathname = usePathname();

  const { sidebarOpen, setSidebarOpen } = useSidebar();

  const [nav, setNav] = useState<INavBarWithChild[]>([
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
      current: false,
      requiredScopes: scopes?.DASHBOARD?.adminPermissons,
    },
    {
      name: "Stores",
      href: "/stores",
      icon: Store,
      current: false,
      requiredScopes: scopes?.STORE?.adminPermissons,
    },
    // {
    //   name: "Option Set",
    //   href: "/option-set",
    //   icon: Logs,
    //   current: false,
    //   requiredScopes: scopes?.OPTION_SET?.adminPermissons,
    // },

    {
      name: "Settings",
      href: "",
      icon: Settings,
      current: false,
      // requiredScopes: [
      //   ...scopes?.ADMIN?.adminPermissons,
      //   ...scopes?.DASHBOARD?.adminPermissons,
      //   ...scopes.STORE.adminPermissons,
      // ],
      child: [
        {
          name: "Subscription Plans",
          href: "/settings/subscription-plans",
          icon: "",
          current: false,
          requiredScopes: scopes?.SUBSCRIPTION_PLAN?.adminPermissons,
        },
        {
          name: "Coupons",
          href: "/settings/coupons",
          icon: "",
          current: false,
          requiredScopes: scopes?.COUPON?.adminPermissons,
        },
        {
          name: "Admins",
          href: "/settings/admins",
          icon: "",
          current: false,
          requiredScopes: scopes?.ADMIN?.adminPermissons,
        },
      ],
    },
  ]);

  const updateCurrent = useCallback((pathname: string) => {
    const newNav = nav.map((item: INavBarWithChild) => {
      if (item?.child) {
        return {
          ...item,
          child: item.child.map((child) => ({ ...child, current: child.href === pathname })),
        };
      }
      return {
        ...item,
        current: item.href === pathname,
      };
    });
    setNav(newNav);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    updateCurrent(pathname);
  }, [pathname, updateCurrent]);

  return (
    <>
      <Transition.Root
        show={sidebarOpen}
        as={Fragment}
      >
        <Dialog
          as="div"
          className="relative z-50 lg:hidden"
          onClose={setSidebarOpen}
        >
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <X
                        className="h-6 w-6 text-white"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Transition.Child>

                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4 ring-1 ring-white/10">
                  <div className="flex h-16 p-2 items-center">
                    <AppLogo />
                  </div>
                  <nav className="flex flex-1 flex-col mt-6">
                    <SidebarMenu nav={nav} />
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
          <div className="flex h-16 p-2 items-center justify-center">
            <AppLogo />
          </div>
          <nav className="flex flex-1 flex-col mt-6">
            <SidebarMenu nav={nav} />
          </nav>
        </div>
      </div>
    </>
  );
}
