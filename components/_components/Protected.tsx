export default function Protected() {
  return (
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 500 500"
      xmlSpace="preserve"
    >
      <g id="BACKGROUND">
        <rect
          style={{ fill: "#FFFFFF" }}
          width={500}
          height={500}
        />
      </g>
      <g id="OBJECTS">
        <ellipse
          style={{ fill: "#EDF0FC" }}
          cx="246.17"
          cy="424.147"
          rx="188.517"
          ry="19.039"
        />
        <g>
          <g>
            <g>
              <g>
                <path
                  style={{ fill: "#EB725F" }}
                  d="M153.514,361.369c-6.277,0.044-3.285,20.4-3.285,20.4s-9.325-22.024-16.498-22.816
						c-7.173-0.792-0.874,18.415-0.874,18.415s-18.642-20.876-23.46-19.65c-4.818,1.226,4.011,12.872,4.011,12.872
						s-30.912-15.548-33.645-9.647c-2.732,5.901,29.121,19.416,29.121,19.416s-14.592,0.801-12.41,5.269
						c2.181,4.467,30.16,5.179,30.16,5.179s-18.722,7.621-13.477,12.579c5.244,4.958,28.071-2.178,28.071-2.178
						s-17.459,10.886-13.432,15.702c4.027,4.816,40.963-14.757,40.963-14.757S159.792,361.324,153.514,361.369z"
                />
                <path
                  style={{ fill: "#F9AB43" }}
                  d="M155.502,377.739l1.836,7.125c0.596,2.379,1.275,4.736,1.913,7.103l1.687,6.166l-5.79-2.773
						l-9.461-4.443c-1.102-2.91-2.205-5.82-3.391-8.699l-1.815-4.4c-0.645-1.452-1.252-2.918-1.929-4.357
						c0.432,1.531,0.934,3.036,1.399,4.555l1.513,4.512c0.868,2.581,1.801,5.138,2.74,7.692l-11.402-5.354l-6.242-2.849
						c-1.226-2.06-2.455-4.119-3.758-6.134l-2.015-3.109c-0.708-1.015-1.382-2.05-2.12-3.047c0.512,1.13,1.089,2.222,1.632,3.334
						l1.737,3.272c0.873,1.64,1.793,3.252,2.721,4.86l-14.413-6.578c-7.506-3.374-15.02-6.73-22.588-9.971
						c7.367,3.674,14.787,7.233,22.216,10.775l14.486,6.804c-1.895,0.351-3.788,0.711-5.671,1.129
						c-2.419,0.523-4.835,1.066-7.229,1.733c2.477-0.197,4.932-0.52,7.384-0.861c2.446-0.327,4.877-0.743,7.307-1.16l6.069,2.851
						l11.528,5.263c-2.596,0.962-5.19,1.93-7.759,2.965c-2.959,1.178-5.911,2.374-8.819,3.69c3.072-0.867,6.101-1.853,9.123-2.857
						c3.001-0.983,5.971-2.051,8.941-3.119l9.444,4.311l5.865,2.623l-5.817,2.74c-2.224,1.058-4.466,2.078-6.672,3.176
						c-4.432,2.151-8.856,4.32-13.227,6.604c4.571-1.85,9.089-3.816,13.599-5.799c2.265-0.97,4.494-2.017,6.741-3.026l6.728-3.053
						l0.04-0.018l4.012,1.795c3.774,1.642,7.531,3.319,11.32,4.929c-3.678-1.848-7.389-3.628-11.082-5.444l-3.964-1.899
						l-0.013-0.044l-2.042-7.067c-0.69-2.353-1.339-4.718-2.07-7.059l-2.147-7.038c-0.756-2.335-1.474-4.68-2.264-7.005
						C154.32,372.977,154.93,375.352,155.502,377.739z"
                />
              </g>
              <g style={{ opacity: "0.1" }}>
                <path
                  style={{ fill: "#0046A0" }}
                  d="M110.02,368.949c-8.093-3.828-28.083-12.7-30.256-8.006c-0.845,1.825,1.621,4.378,5.426,7.034
						C92.35,366.676,100.92,366.975,110.02,368.949z"
                />
                <path
                  style={{ fill: "#0046A0" }}
                  d="M132.786,377.144c14.652,7.464,25.601,17.779,30.445,27.839c3.351-1.677,5.529-2.83,5.529-2.83
						s-8.967-40.828-15.245-40.784c-6.277,0.044-3.285,20.4-3.285,20.4s-9.325-22.024-16.498-22.816
						C127.014,358.211,132.111,375.008,132.786,377.144z"
                />
                <path
                  style={{ fill: "#0046A0" }}
                  d="M112.67,369.571c4.793,1.199,9.705,2.85,14.616,4.978c1.8,0.78,3.551,1.606,5.256,2.469
						c-2.43-2.686-18.69-20.433-23.144-19.299C105.453,358.722,110.654,366.708,112.67,369.571z"
                />
              </g>
            </g>
            <g>
              <g>
                <path
                  style={{ fill: "#EB725F" }}
                  d="M375.85,363.891c0.642-6.245-19.919-5.495-19.919-5.495s22.911-6.862,24.483-13.905
						c1.572-7.044-18.209-2.882-18.209-2.882s22.789-16.248,22.096-21.171c-0.692-4.923-13.233,2.579-13.233,2.579
						s18.835-29.027,13.267-32.388c-5.567-3.361-22.483,26.824-22.483,26.824s0.799-14.592-3.88-12.912
						c-4.679,1.68-8.445,29.413-8.445,29.413s-5.529-19.443-11.03-14.772c-5.501,4.671-0.904,28.141-0.904,28.141
						s-8.912-18.545-14.139-15.068c-5.227,3.477,10.191,42.331,10.191,42.331S375.207,370.136,375.85,363.891z"
                />
                <path
                  style={{ fill: "#F9AB43" }}
                  d="M359.361,364.077l-7.283,1.047c-2.43,0.333-4.847,0.749-7.27,1.125l-6.313,1.003l3.39-5.452
						l5.451-8.919c3.014-0.777,6.026-1.556,9.018-2.42l4.572-1.323c1.513-0.482,3.037-0.926,4.542-1.441
						c-1.569,0.262-3.12,0.597-4.681,0.893l-4.651,1.011c-2.66,0.58-5.304,1.228-7.945,1.883l6.569-10.748l3.514-5.893
						c2.182-0.994,4.362-1.99,6.508-3.065l3.311-1.663c1.086-0.593,2.189-1.149,3.261-1.774c-1.179,0.386-2.328,0.84-3.493,1.257
						l-3.443,1.368c-1.726,0.688-3.429,1.426-5.128,2.173l8.115-13.608c4.174-7.092,8.332-14.195,12.381-21.362
						c-4.457,6.921-8.806,13.908-13.139,20.905l-8.347,13.655c-0.142-1.922-0.292-3.843-0.502-5.76
						c-0.256-2.462-0.531-4.923-0.932-7.375c-0.075,2.484-0.023,4.96,0.048,7.434c0.058,2.467,0.206,4.928,0.354,7.39l-3.497,5.721
						l-6.492,10.884c-0.672-2.686-1.351-5.37-2.099-8.037c-0.848-3.07-1.714-6.135-2.703-9.17c0.525,3.149,1.175,6.267,1.842,9.381
						c0.65,3.091,1.386,6.16,2.123,9.229l-5.318,8.916l-3.249,5.543l-2.088-6.082c-0.808-2.327-1.578-4.666-2.427-6.979
						c-1.654-4.641-3.326-9.275-5.119-13.869c1.34,4.746,2.8,9.452,4.278,14.151c0.717,2.357,1.513,4.688,2.271,7.031l2.3,7.022
						l0.014,0.042l-2.222,3.792c-2.044,3.572-4.123,7.123-6.137,10.713c2.239-3.454,4.414-6.948,6.623-10.42l2.321-3.732
						l0.046-0.008l7.248-1.257c2.414-0.428,4.836-0.815,7.243-1.286l7.23-1.365c2.403-0.496,4.813-0.954,7.21-1.484
						C364.223,363.423,361.795,363.77,359.361,364.077z"
                />
              </g>
              <g style={{ opacity: "0.1" }}>
                <path
                  style={{ fill: "#0046A0" }}
                  d="M373.07,319.828c4.69-7.626,15.694-26.526,11.266-29.199c-1.722-1.039-4.529,1.133-7.586,4.625
						C377.261,302.513,376.027,310.999,373.07,319.828z"
                />
                <path
                  style={{ fill: "#0046A0" }}
                  d="M362.435,341.562c-9.021,13.749-20.471,23.504-31,27.219c1.3,3.514,2.209,5.805,2.209,5.805
						s41.564-4.45,42.206-10.695c0.643-6.245-19.919-5.495-19.919-5.495s22.911-6.862,24.483-13.905
						C381.885,337.895,364.632,341.125,362.435,341.562z"
                />
                <path
                  style={{ fill: "#0046A0" }}
                  d="M372.162,322.395c-1.716,4.633-3.894,9.336-6.546,13.984c-0.972,1.704-1.985,3.354-3.029,4.954
						c2.936-2.122,22.354-16.344,21.714-20.895C383.734,316.407,375.228,320.704,372.162,322.395z"
                />
              </g>
            </g>
          </g>
          <g>
            <g>
              <g>
                <path
                  style={{ fill: "#6E7FDD" }}
                  d="M98.485,301.004c1.132,2.607,2.545,5.113,3.984,7.559c3.599,6.12,7.782,11.897,12.428,17.264
						c6.347,7.333,13.622,13.869,21.711,19.229c9.669,6.408,20.489,11.017,31.834,13.454c14.038,3.015,28.585,2.662,42.644-0.033
						c2.043-0.392,4.076-0.834,6.099-1.318c0.629-0.15,0.309-1.105-0.318-0.955c-14.364,3.432-29.355,4.571-43.974,2.13
						c-11.568-1.932-22.68-6.087-32.696-12.185c-8.388-5.106-15.952-11.513-22.57-18.756c-4.986-5.458-9.451-11.404-13.312-17.708
						c-1.767-2.886-3.338-5.998-4.816-8.866c-0.022-0.043-0.04-0.088-0.059-0.132C99.185,300.097,98.226,300.408,98.485,301.004
						L98.485,301.004z"
                />
              </g>
              <g>
                <g>
                  <path
                    style={{ fill: "#6E7FDD" }}
                    d="M103.685,310.575c-2.485-1.521-4.179-3.37-5.674-5.301c-1.456-1.947-2.621-4.015-3.525-6.191
							c-0.908-2.174-1.549-4.459-1.897-6.866c-0.311-2.422-0.417-4.928,0.273-7.765c2.494,1.517,4.191,3.365,5.685,5.296
							c1.456,1.947,2.619,4.016,3.515,6.195c0.9,2.178,1.538,4.464,1.887,6.87C104.256,305.237,104.366,307.742,103.685,310.575z"
                  />
                  <path
                    style={{ fill: "#0046A0" }}
                    d="M93.794,286.704c0.855,1.738,1.649,3.501,2.433,5.268c0.786,1.766,1.521,3.554,2.285,5.329
							c0.718,1.795,1.46,3.579,2.155,5.383c0.695,1.804,1.381,3.612,2.005,5.445c-0.856-1.737-1.649-3.501-2.434-5.267
							c-0.784-1.767-1.522-3.553-2.283-5.33c-0.717-1.795-1.461-3.579-2.155-5.383C95.105,290.345,94.419,288.537,93.794,286.704z"
                  />
                </g>
                <path
                  style={{ opacity: "0.1", fill: "#0046A0" }}
                  d="M98.546,289.748c-1.493-1.932-3.191-3.779-5.685-5.296
						c-0.09,0.37-0.165,0.733-0.229,1.092c1.491,1.204,2.668,2.538,3.734,3.918c1.456,1.947,2.619,4.016,3.515,6.195
						c0.9,2.178,1.538,4.464,1.887,6.87c0.268,2.104,0.384,4.271-0.035,6.659c0.597,0.485,1.239,0.951,1.952,1.387
						c0.681-2.833,0.571-5.338,0.262-7.76c-0.348-2.406-0.986-4.692-1.887-6.87C101.165,293.764,100.002,291.696,98.546,289.748z"
                />
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M108.874,318.059c-1.572-2.453-2.29-4.855-2.794-7.245c-0.461-2.387-0.608-4.756-0.472-7.109
								c0.131-2.353,0.551-4.689,1.288-7.006c0.777-2.315,1.774-4.617,3.632-6.868c1.582,2.453,2.304,4.856,2.805,7.246
								c0.461,2.387,0.605,4.756,0.461,7.108c-0.14,2.352-0.563,4.688-1.299,7.005C111.716,313.505,110.722,315.807,108.874,318.059
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M110.386,292.264c0.011,1.937-0.044,3.869-0.108,5.801c-0.063,1.932-0.181,3.861-0.269,5.792
								c-0.137,1.928-0.247,3.858-0.408,5.784c-0.162,1.927-0.333,3.853-0.571,5.775c-0.012-1.937,0.043-3.87,0.107-5.802
								c0.065-1.932,0.18-3.861,0.27-5.792c0.138-1.928,0.247-3.858,0.409-5.784C109.977,296.112,110.148,294.186,110.386,292.264z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M113.334,297.077c-0.501-2.39-1.223-4.792-2.805-7.246
							c-0.242,0.293-0.468,0.588-0.682,0.883c0.816,1.734,1.293,3.448,1.651,5.155c0.461,2.387,0.605,4.756,0.461,7.108
							c-0.14,2.352-0.563,4.688-1.299,7.005c-0.677,2.01-1.517,4.011-2.936,5.977c0.326,0.697,0.7,1.396,1.151,2.1
							c1.848-2.252,2.842-4.554,3.621-6.869c0.736-2.317,1.159-4.653,1.299-7.005C113.939,301.833,113.795,299.464,113.334,297.077z
							"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M108.697,317.577c-2.69,1.117-5.182,1.402-7.623,1.478c-2.431,0.034-4.789-0.237-7.081-0.785
								c-2.293-0.542-4.519-1.366-6.671-2.498c-2.143-1.171-4.233-2.558-6.124-4.782c2.693-1.127,5.185-1.415,7.626-1.489
								c2.431-0.035,4.789,0.24,7.079,0.795c2.291,0.551,4.516,1.377,6.668,2.509C104.712,313.979,106.804,315.362,108.697,317.577z
								"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M83.569,311.557c1.909,0.329,3.802,0.723,5.692,1.126c1.891,0.402,3.769,0.857,5.655,1.282
								c1.874,0.473,3.754,0.921,5.622,1.418c1.868,0.498,3.734,1.004,5.584,1.577c-1.909-0.328-3.802-0.722-5.693-1.125
								c-1.891-0.404-3.77-0.856-5.655-1.283c-1.874-0.475-3.754-0.921-5.622-1.419C87.285,312.635,85.419,312.128,83.569,311.557z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M88.824,309.5c-2.441,0.073-4.933,0.362-7.626,1.489
							c0.246,0.29,0.496,0.564,0.749,0.827c1.85-0.499,3.621-0.668,5.365-0.72c2.431-0.035,4.789,0.24,7.079,0.795
							c2.291,0.551,4.516,1.378,6.668,2.509c1.86,1.019,3.682,2.198,5.369,3.94c0.743-0.198,1.497-0.444,2.27-0.765
							c-1.893-2.215-3.984-3.598-6.126-4.772c-2.152-1.132-4.377-1.958-6.668-2.509C93.613,309.74,91.255,309.466,88.824,309.5z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M120.062,330.476c-1.938-2.175-3.025-4.435-3.898-6.716c-0.83-2.285-1.347-4.602-1.583-6.947
								c-0.24-2.344-0.193-4.717,0.171-7.121c0.403-2.408,1.027-4.838,2.508-7.354c1.948,2.174,3.038,4.434,3.909,6.715
								c0.831,2.285,1.345,4.602,1.571,6.948c0.231,2.345,0.181,4.718-0.182,7.122C122.153,325.532,121.533,327.962,120.062,330.476
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M117.502,304.764c0.316,1.911,0.565,3.828,0.804,5.747c0.241,1.918,0.428,3.842,0.644,5.762
								c0.168,1.926,0.362,3.848,0.505,5.776c0.143,1.928,0.277,3.857,0.343,5.792c-0.316-1.911-0.566-3.828-0.806-5.746
								c-0.239-1.918-0.428-3.842-0.643-5.763c-0.166-1.926-0.362-3.848-0.504-5.777C117.703,308.629,117.57,306.7,117.502,304.764z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M121.17,309.054c-0.87-2.281-1.961-4.541-3.909-6.715
							c-0.193,0.328-0.37,0.654-0.535,0.979c1.078,1.584,1.819,3.202,2.44,4.831c0.83,2.285,1.345,4.602,1.572,6.948
							c0.231,2.345,0.181,4.718-0.182,7.122c-0.353,2.091-0.868,4.199-1.961,6.364c0.431,0.637,0.911,1.269,1.467,1.893
							c1.471-2.514,2.091-4.944,2.497-7.353c0.363-2.404,0.414-4.777,0.182-7.122C122.515,313.656,122,311.339,121.17,309.054z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M119.811,330.028c-2.482,1.526-4.897,2.198-7.296,2.657c-2.396,0.415-4.767,0.518-7.117,0.338
								c-2.35-0.175-4.677-0.639-6.98-1.419c-2.3-0.82-4.583-1.861-6.799-3.761c2.482-1.536,4.898-2.212,7.297-2.668
								c2.396-0.416,4.767-0.516,7.116-0.327c2.349,0.184,4.677,0.651,6.979,1.431C115.311,327.1,117.594,328.138,119.811,330.028z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M94.049,328.03c1.937,0.025,3.868,0.116,5.799,0.218c1.931,0.1,3.857,0.254,5.786,0.378
								c1.925,0.173,3.852,0.32,5.775,0.517c1.923,0.198,3.845,0.405,5.763,0.68c-1.937-0.024-3.868-0.116-5.799-0.217
								c-1.931-0.101-3.857-0.253-5.786-0.379c-1.925-0.175-3.852-0.319-5.775-0.518C97.889,328.511,95.966,328.304,94.049,328.03z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M98.916,325.174c-2.399,0.456-4.815,1.132-7.297,2.668
							c0.289,0.247,0.579,0.479,0.87,0.699c1.749-0.783,3.472-1.228,5.185-1.554c2.396-0.416,4.767-0.516,7.116-0.327
							c2.349,0.184,4.676,0.651,6.979,1.431c1.997,0.715,3.981,1.592,5.921,3.048c0.703-0.313,1.409-0.674,2.121-1.112
							c-2.217-1.89-4.5-2.927-6.8-3.75c-2.303-0.78-4.63-1.247-6.979-1.431C103.683,324.658,101.312,324.758,98.916,325.174z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M133.448,342.106c-2.26-1.839-3.692-3.897-4.917-6.01c-1.183-2.124-2.063-4.329-2.668-6.606
								c-0.61-2.276-0.941-4.626-0.965-7.058c0.015-2.442,0.244-4.94,1.305-7.659c2.269,1.836,3.705,3.893,4.928,6.007
								c1.184,2.124,2.061,4.329,2.658,6.609c0.602,2.278,0.93,4.629,0.954,7.06C134.725,336.892,134.5,339.389,133.448,342.106z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M126.827,317.129c0.616,1.836,1.167,3.69,1.709,5.545c0.543,1.855,1.034,3.725,1.554,5.586
								c0.472,1.874,0.97,3.742,1.419,5.622c0.448,1.881,0.887,3.764,1.261,5.664c-0.617-1.836-1.168-3.689-1.71-5.545
								c-0.542-1.856-1.035-3.725-1.553-5.587c-0.471-1.875-0.97-3.742-1.417-5.622C127.641,320.913,127.202,319.03,126.827,317.129
								z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M131.131,320.78c-1.222-2.114-2.659-4.171-4.928-6.007
							c-0.138,0.354-0.261,0.705-0.373,1.052c1.317,1.392,2.305,2.871,3.179,4.381c1.184,2.124,2.061,4.329,2.658,6.609
							c0.602,2.278,0.93,4.629,0.954,7.06c-0.015,2.121-0.189,4.284-0.923,6.595c0.527,0.56,1.101,1.107,1.75,1.635
							c1.052-2.717,1.277-5.214,1.295-7.656c-0.024-2.431-0.352-4.782-0.954-7.06C133.191,325.11,132.314,322.904,131.131,320.78z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M133.128,341.703c-2.207,1.901-4.485,2.95-6.78,3.785c-2.299,0.791-4.624,1.27-6.972,1.466
								c-2.348,0.201-4.719,0.114-7.117-0.29c-2.401-0.443-4.82-1.108-7.311-2.631c2.206-1.911,4.484-2.964,6.779-3.796
								c2.299-0.792,4.624-1.268,6.973-1.455c2.348-0.192,4.72-0.102,7.118,0.301C128.219,339.53,130.638,340.19,133.128,341.703z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M107.377,343.832c1.916-0.283,3.837-0.501,5.759-0.708c1.922-0.209,3.848-0.364,5.772-0.548
								c1.928-0.136,3.854-0.297,5.784-0.409c1.93-0.111,3.861-0.212,5.797-0.246c-1.916,0.284-3.837,0.502-5.759,0.709
								c-1.922,0.207-3.848,0.364-5.772,0.547c-1.928,0.134-3.854,0.298-5.784,0.408
								C111.244,343.696,109.313,343.797,107.377,343.832z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M111.727,340.237c-2.296,0.832-4.573,1.885-6.779,3.796
							c0.325,0.199,0.648,0.381,0.97,0.552c1.602-1.052,3.232-1.765,4.871-2.359c2.299-0.792,4.624-1.268,6.973-1.455
							c2.349-0.192,4.721-0.102,7.118,0.301c2.085,0.387,4.184,0.938,6.331,2.067c0.644-0.42,1.284-0.889,1.917-1.435
							c-2.49-1.513-4.909-2.173-7.31-2.62c-2.398-0.403-4.77-0.493-7.118-0.301C116.351,338.97,114.026,339.445,111.727,340.237z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M150.153,351.748c-2.57-1.372-4.371-3.117-5.978-4.956c-1.568-1.858-2.854-3.853-3.885-5.972
								c-1.035-2.117-1.81-4.36-2.299-6.742c-0.454-2.399-0.708-4.895-0.187-7.767c2.579,1.367,4.383,3.111,5.988,4.951
								c1.569,1.858,2.852,3.854,3.875,5.977c1.027,2.121,1.8,4.365,2.29,6.747C150.407,346.386,150.665,348.88,150.153,351.748z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M138.868,328.504c0.956,1.684,1.853,3.397,2.74,5.115c0.889,1.717,1.729,3.458,2.596,5.185
								c0.823,1.749,1.669,3.487,2.47,5.246c0.8,1.76,1.592,3.524,2.323,5.317c-0.957-1.684-1.853-3.397-2.741-5.114
								c-0.887-1.718-1.729-3.457-2.595-5.185c-0.821-1.75-1.67-3.486-2.469-5.247C140.391,332.061,139.6,330.297,138.868,328.504z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M143.791,331.262c-1.605-1.84-3.409-3.584-5.988-4.951
							c-0.068,0.374-0.121,0.741-0.164,1.104c1.56,1.114,2.813,2.376,3.959,3.691c1.569,1.858,2.852,3.854,3.875,5.977
							c1.027,2.121,1.8,4.365,2.29,6.746c0.392,2.085,0.636,4.241,0.359,6.65c0.624,0.449,1.293,0.876,2.031,1.27
							c0.512-2.868,0.254-5.362-0.197-7.763c-0.49-2.382-1.262-4.626-2.29-6.747C146.643,335.116,145.36,333.12,143.791,331.262z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M149.762,351.414c-1.802,2.289-3.836,3.755-5.929,5.014c-2.105,1.217-4.294,2.133-6.562,2.776
								c-2.266,0.647-4.61,1.016-7.041,1.079c-2.442,0.025-4.943-0.163-7.68-1.181c1.799-2.299,3.832-3.768,5.926-5.025
								c2.104-1.218,4.295-2.131,6.565-2.765c2.268-0.639,4.613-1.005,7.044-1.069C144.528,350.222,147.029,350.406,149.762,351.414
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M124.897,358.44c1.826-0.645,3.67-1.227,5.517-1.799c1.846-0.573,3.707-1.094,5.56-1.644
								c1.866-0.503,3.725-1.031,5.598-1.51c1.873-0.479,3.749-0.948,5.643-1.353c-1.826,0.646-3.67,1.228-5.516,1.8
								c-1.847,0.572-3.707,1.095-5.561,1.643c-1.867,0.501-3.725,1.031-5.599,1.509C128.666,357.565,126.79,358.034,124.897,358.44
								z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M128.477,354.078c-2.094,1.257-4.127,2.726-5.926,5.025
							c0.356,0.133,0.709,0.25,1.058,0.355c1.37-1.339,2.833-2.352,4.329-3.249c2.104-1.218,4.295-2.131,6.565-2.765
							c2.268-0.639,4.613-1.005,7.044-1.069c2.121-0.019,4.286,0.119,6.609,0.815c0.551-0.536,1.09-1.119,1.607-1.776
							c-2.733-1.008-5.234-1.192-7.676-1.17c-2.431,0.064-4.776,0.43-7.044,1.069C132.772,351.947,130.581,352.859,128.477,354.078z
							"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M165.91,357.638c-2.665-1.175-4.592-2.781-6.332-4.495c-1.703-1.735-3.135-3.629-4.321-5.664
								c-1.191-2.033-2.132-4.212-2.798-6.551c-0.632-2.359-1.072-4.828-0.768-7.732c2.674,1.17,4.604,2.774,6.342,4.489
								c1.704,1.735,3.133,3.63,4.312,5.67c1.183,2.038,2.121,4.218,2.788,6.556C165.762,352.272,166.206,354.74,165.91,357.638z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M152.917,335.304c1.079,1.608,2.102,3.249,3.116,4.895c1.015,1.645,1.983,3.318,2.977,4.976
								c0.951,1.682,1.925,3.352,2.856,5.046c0.93,1.695,1.851,3.395,2.715,5.128c-1.08-1.607-2.103-3.249-3.117-4.895
								c-1.013-1.646-1.983-3.318-2.975-4.977c-0.95-1.683-1.926-3.352-2.855-5.047
								C154.703,338.737,153.781,337.037,152.917,335.304z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M158.033,337.686c-1.738-1.715-3.668-3.319-6.342-4.489
							c-0.04,0.378-0.065,0.749-0.081,1.113c1.639,0.994,2.983,2.159,4.225,3.384c1.704,1.735,3.133,3.63,4.312,5.67
							c1.183,2.038,2.121,4.218,2.788,6.556c0.547,2.049,0.952,4.181,0.856,6.604c0.656,0.401,1.355,0.777,2.12,1.114
							c0.296-2.898-0.148-5.366-0.778-7.726c-0.667-2.338-1.605-4.518-2.788-6.556C161.166,341.316,159.737,339.421,158.033,337.686
							z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M165.496,357.334c-1.625,2.418-3.544,4.032-5.537,5.444c-2.007,1.372-4.123,2.448-6.335,3.259
								c-2.211,0.815-4.521,1.358-6.94,1.603c-2.433,0.208-4.942,0.207-7.746-0.602c1.622-2.427,3.54-4.045,5.533-5.454
								c2.007-1.372,4.124-2.446,6.339-3.249c2.214-0.807,4.525-1.347,6.944-1.593C160.187,356.537,162.695,356.534,165.496,357.334
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M141.226,366.202c1.773-0.78,3.568-1.498,5.367-2.207c1.798-0.71,3.615-1.369,5.422-2.056
								c1.823-0.641,3.638-1.307,5.47-1.925c1.832-0.618,3.667-1.226,5.526-1.772c-1.772,0.781-3.567,1.499-5.366,2.208
								c-1.799,0.708-3.615,1.37-5.422,2.055c-1.824,0.639-3.638,1.307-5.47,1.924C144.92,365.047,143.084,365.656,141.226,366.202z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M144.47,361.584c-1.994,1.41-3.911,3.028-5.533,5.454
							c0.366,0.105,0.725,0.196,1.081,0.275c1.266-1.438,2.65-2.557,4.074-3.564c2.007-1.372,4.124-2.446,6.339-3.249
							c2.214-0.807,4.525-1.347,6.944-1.593c2.113-0.178,4.283-0.202,6.652,0.318c0.51-0.576,1.002-1.198,1.469-1.891
							c-2.801-0.8-5.309-0.797-7.742-0.592c-2.419,0.246-4.73,0.786-6.944,1.593C148.593,359.138,146.477,360.212,144.47,361.584z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M186.075,360.244c-2.855-0.578-5.08-1.734-7.147-3.036c-2.035-1.331-3.838-2.874-5.433-4.609
								c-1.598-1.732-2.983-3.659-4.134-5.801c-1.122-2.169-2.08-4.487-2.404-7.388c2.863,0.571,5.09,1.725,7.155,3.028
								c2.035,1.331,3.837,2.876,5.425,4.617c1.592,1.737,2.975,3.667,4.126,5.808C184.782,355.034,185.744,357.349,186.075,360.244
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M168.604,341.206c1.398,1.34,2.748,2.724,4.091,4.116c1.343,1.39,2.647,2.817,3.972,4.224
								c1.289,1.44,2.598,2.862,3.869,4.319c1.271,1.457,2.535,2.92,3.749,4.429c-1.399-1.339-2.749-2.724-4.091-4.115
								c-1.342-1.392-2.647-2.817-3.971-4.225c-1.288-1.441-2.598-2.862-3.868-4.32
								C171.083,344.178,169.819,342.714,168.604,341.206z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M174.112,342.438c-2.065-1.303-4.293-2.457-7.155-3.028
							c0.042,0.378,0.096,0.745,0.159,1.104c1.813,0.62,3.376,1.471,4.851,2.402c2.035,1.33,3.837,2.876,5.425,4.616
							c1.592,1.737,2.975,3.667,4.126,5.808c0.972,1.885,1.824,3.881,2.249,6.268c0.727,0.251,1.49,0.469,2.309,0.635
							c-0.331-2.894-1.292-5.21-2.412-7.381c-1.151-2.141-2.534-4.07-4.126-5.808C177.948,345.314,176.147,343.769,174.112,342.438z
							"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M185.604,360.036c-1.07,2.709-2.6,4.697-4.244,6.502c-1.668,1.769-3.504,3.274-5.492,4.539
								c-1.985,1.269-4.126,2.294-6.437,3.051c-2.332,0.723-4.783,1.26-7.696,1.069c1.065-2.718,2.592-4.708,4.238-6.512
								c1.667-1.77,3.505-3.272,5.498-4.529c1.99-1.262,4.132-2.284,6.442-3.042C180.248,360.393,182.697,359.853,185.604,360.036z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M163.794,373.889c1.565-1.141,3.165-2.227,4.77-3.304c1.605-1.078,3.238-2.111,4.856-3.168
								c1.644-1.016,3.274-2.054,4.931-3.05c1.658-0.995,3.32-1.982,5.019-2.913c-1.564,1.142-3.164,2.228-4.77,3.305
								c-1.606,1.077-3.238,2.111-4.857,3.167c-1.645,1.015-3.274,2.055-4.932,3.049
								C167.154,371.971,165.492,372.958,163.794,373.889z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M165.974,368.685c-1.646,1.803-3.173,3.794-4.238,6.512
							c0.38,0.025,0.751,0.036,1.115,0.038c0.929-1.676,2.041-3.065,3.217-4.353c1.667-1.77,3.505-3.272,5.497-4.529
							c1.99-1.262,4.132-2.284,6.443-3.042c2.026-0.626,4.141-1.114,6.566-1.112c0.375-0.671,0.723-1.384,1.031-2.162
							c-2.908-0.183-5.356,0.357-7.69,1.078c-2.31,0.758-4.452,1.78-6.442,3.042C169.479,365.413,167.642,366.915,165.974,368.685z"
                  />
                </g>
              </g>
            </g>
            <g>
              <g>
                <path
                  style={{ fill: "#6E7FDD" }}
                  d="M416.53,365.924c-1.979,2.04-4.186,3.886-6.395,5.666c-5.528,4.456-11.48,8.385-17.721,11.767
						c-8.527,4.621-17.64,8.169-27.101,10.329c-11.309,2.582-23.059,3.076-34.534,1.352c-14.199-2.134-27.685-7.598-39.888-15.081
						c-1.773-1.087-3.519-2.219-5.242-3.385c-0.536-0.363,0.101-1.143,0.635-0.782c12.229,8.281,25.852,14.637,40.392,17.513
						c11.505,2.275,23.369,2.309,34.892,0.139c9.65-1.818,18.988-5.142,27.737-9.584c6.591-3.347,12.868-7.335,18.705-11.87
						c2.672-2.077,5.24-4.434,7.636-6.596c0.036-0.032,0.068-0.069,0.102-0.103C416.196,364.828,416.983,365.458,416.53,365.924
						L416.53,365.924z"
                />
              </g>
              <g>
                <g>
                  <path
                    style={{ fill: "#6E7FDD" }}
                    d="M408.287,373.043c2.861-0.546,5.099-1.678,7.18-2.957c2.049-1.309,3.87-2.832,5.483-4.549
							c1.617-1.714,3.023-3.625,4.198-5.755c1.146-2.156,2.13-4.464,2.485-7.361c-2.869,0.54-5.109,1.669-7.188,2.949
							c-2.05,1.308-3.868,2.833-5.475,4.556c-1.611,1.72-3.015,3.634-4.19,5.762C409.637,367.847,408.65,370.152,408.287,373.043z"
                  />
                  <path
                    style={{ fill: "#0046A0" }}
                    d="M425.966,354.199c-1.413,1.324-2.778,2.694-4.136,4.07c-1.359,1.375-2.678,2.788-4.018,4.18
							c-1.305,1.426-2.629,2.833-3.917,4.276c-1.287,1.443-2.567,2.892-3.798,4.387c1.414-1.324,2.779-2.693,4.137-4.069
							c1.358-1.377,2.678-2.788,4.018-4.181c1.304-1.427,2.63-2.833,3.916-4.277C423.455,357.143,424.735,355.694,425.966,354.199z"
                  />
                </g>
                <path
                  style={{ opacity: "0.1", fill: "#0046A0" }}
                  d="M420.446,355.371c2.079-1.281,4.319-2.41,7.188-2.949
						c-0.046,0.378-0.104,0.744-0.171,1.103c-1.82,0.6-3.392,1.434-4.877,2.348c-2.05,1.308-3.868,2.833-5.475,4.556
						c-1.611,1.72-3.015,3.634-4.19,5.762c-0.993,1.874-1.867,3.86-2.318,6.243c-0.73,0.243-1.495,0.452-2.316,0.609
						c0.363-2.891,1.35-5.196,2.493-7.354c1.175-2.129,2.579-4.042,4.19-5.762C416.578,358.204,418.396,356.679,420.446,355.371z"
                />
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M400.79,378.214c2.337-1.74,3.857-3.734,5.171-5.793c1.274-2.071,2.247-4.236,2.951-6.485
								c0.708-2.247,1.139-4.581,1.268-7.009c0.09-2.44-0.031-4.946-0.974-7.708c-2.346,1.737-3.87,3.73-5.182,5.789
								c-1.274,2.071-2.245,4.237-2.94,6.488c-0.699,2.25-1.128,4.585-1.257,7.013C399.739,372.95,399.856,375.455,400.79,378.214z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M408.48,353.546c-0.694,1.808-1.325,3.636-1.946,5.467c-0.623,1.83-1.193,3.677-1.793,5.514
								c-0.553,1.852-1.13,3.697-1.659,5.556c-0.529,1.86-1.048,3.722-1.504,5.604c0.695-1.808,1.326-3.635,1.947-5.466
								c0.621-1.831,1.194-3.677,1.792-5.515c0.551-1.853,1.131-3.696,1.658-5.556C407.504,357.291,408.024,355.428,408.48,353.546z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M404.024,357.008c1.312-2.059,2.836-4.052,5.182-5.789
							c0.123,0.36,0.231,0.715,0.327,1.067c-1.376,1.334-2.427,2.77-3.364,4.24c-1.274,2.071-2.245,4.237-2.94,6.488
							c-0.699,2.25-1.128,4.585-1.257,7.013c-0.076,2.12,0.004,4.288,0.638,6.629c-0.551,0.537-1.148,1.059-1.819,1.558
							c-0.934-2.759-1.051-5.264-0.964-7.705c0.129-2.428,0.558-4.762,1.257-7.013C401.778,361.245,402.749,359.079,404.024,357.008
							z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M401.127,377.825c2.123,1.995,4.354,3.141,6.611,4.073c2.263,0.89,4.565,1.468,6.902,1.765
								c2.337,0.302,4.71,0.317,7.123,0.017c2.418-0.34,4.864-0.899,7.417-2.313c-2.122-2.005-4.352-3.154-6.609-4.084
								c-2.263-0.891-4.565-1.466-6.904-1.754c-2.338-0.293-4.712-0.305-7.125-0.006
								C406.124,375.865,403.679,376.421,401.127,377.825z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M426.762,381.062c-1.902-0.366-3.812-0.666-5.723-0.956c-1.911-0.291-3.829-0.529-5.744-0.796
								c-1.92-0.219-3.837-0.463-5.761-0.658c-1.923-0.194-3.848-0.378-5.781-0.496c1.902,0.367,3.812,0.666,5.723,0.957
								c1.911,0.29,3.829,0.529,5.744,0.795c1.921,0.217,3.838,0.464,5.761,0.656C422.904,380.759,424.829,380.944,426.762,381.062z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M422.571,377.283c2.258,0.93,4.487,2.08,6.609,4.084
							c-0.333,0.184-0.664,0.353-0.993,0.509c-1.555-1.12-3.153-1.903-4.765-2.567c-2.263-0.891-4.565-1.466-6.904-1.754
							c-2.338-0.293-4.712-0.305-7.125-0.006c-2.1,0.297-4.221,0.757-6.414,1.792c-0.625-0.448-1.244-0.944-1.854-1.516
							c2.552-1.404,4.998-1.96,7.416-2.302c2.413-0.3,4.786-0.288,7.125,0.006C418.006,375.817,420.309,376.392,422.571,377.283z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M385.94,385.884c2.581-1.351,4.395-3.082,6.017-4.908c1.583-1.845,2.885-3.83,3.932-5.941
								c1.052-2.108,1.845-4.345,2.353-6.723c0.473-2.396,0.747-4.889,0.249-7.766c-2.59,1.347-4.408,3.076-6.027,4.903
								c-1.584,1.845-2.883,3.831-3.922,5.946c-1.044,2.112-1.834,4.351-2.343,6.728C385.729,380.52,385.451,383.012,385.94,385.884
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M397.41,362.73c-0.97,1.676-1.88,3.382-2.781,5.093c-0.902,1.71-1.756,3.444-2.637,5.164
								c-0.837,1.742-1.697,3.473-2.512,5.226c-0.814,1.754-1.62,3.511-2.365,5.299c0.97-1.676,1.88-3.382,2.782-5.092
								c0.901-1.71,1.757-3.443,2.636-5.165c0.835-1.743,1.697-3.473,2.51-5.227C395.858,366.275,396.663,364.517,397.41,362.73z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M392.465,365.449c1.619-1.827,3.437-3.557,6.027-4.903
							c0.065,0.375,0.115,0.742,0.155,1.105c-1.568,1.101-2.832,2.354-3.989,3.659c-1.584,1.845-2.883,3.831-3.922,5.946
							c-1.044,2.112-1.834,4.351-2.343,6.728c-0.408,2.081-0.67,4.235-0.412,6.647c-0.628,0.444-1.3,0.866-2.041,1.253
							c-0.489-2.872-0.211-5.364,0.259-7.761c0.509-2.378,1.299-4.616,2.343-6.728C389.582,369.28,390.881,367.294,392.465,365.449z
							"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M386.334,385.553c1.784,2.303,3.806,3.786,5.889,5.061c2.095,1.234,4.278,2.167,6.539,2.827
								c2.26,0.665,4.602,1.053,7.032,1.135c2.441,0.044,4.944-0.124,7.689-1.12c-1.781-2.313-3.802-3.799-5.886-5.072
								c-2.095-1.235-4.278-2.165-6.542-2.817c-2.263-0.657-4.605-1.042-7.035-1.125
								C391.577,384.402,389.075,384.567,386.334,385.553z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M411.142,392.776c-1.821-0.66-3.66-1.256-5.502-1.843c-1.842-0.588-3.699-1.124-5.547-1.688
								c-1.862-0.518-3.717-1.06-5.586-1.554c-1.869-0.493-3.741-0.978-5.632-1.398c1.82,0.661,3.66,1.257,5.502,1.844
								c1.842,0.586,3.698,1.124,5.547,1.687c1.863,0.516,3.717,1.061,5.586,1.553C407.38,391.871,409.252,392.355,411.142,392.776z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M407.597,388.386c2.084,1.273,4.105,2.759,5.886,5.072
							c-0.358,0.13-0.711,0.244-1.06,0.347c-1.36-1.35-2.815-2.374-4.303-3.284c-2.095-1.235-4.278-2.165-6.542-2.817
							c-2.263-0.657-4.605-1.041-7.035-1.125c-2.121-0.036-4.287,0.085-6.616,0.763c-0.547-0.54-1.08-1.128-1.592-1.789
							c2.741-0.986,5.243-1.151,7.686-1.109c2.43,0.083,4.772,0.468,7.035,1.125C403.319,386.221,405.502,387.151,407.597,388.386z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M369.311,392.041c2.763-0.923,4.83-2.343,6.722-3.888c1.857-1.57,3.458-3.322,4.828-5.239
								c1.375-1.914,2.513-3.996,3.393-6.263c0.848-2.29,1.516-4.708,1.482-7.627c-2.771,0.917-4.841,2.335-6.731,3.881
								c-1.857,1.569-3.456,3.323-4.819,5.246c-1.367,1.919-2.504,4.003-3.385,6.269
								C369.956,386.711,369.286,389.128,369.311,392.041z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M384.321,371.008c-1.224,1.501-2.394,3.04-3.556,4.585c-1.163,1.544-2.282,3.12-3.425,4.678
								c-1.103,1.587-2.228,3.159-3.311,4.76c-1.083,1.601-2.158,3.208-3.179,4.854c1.225-1.5,2.395-3.04,3.557-4.585
								c1.162-1.545,2.283-3.12,3.424-4.679c1.102-1.588,2.228-3.158,3.311-4.76C382.224,374.261,383.299,372.654,384.321,371.008z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M379.006,372.905c1.89-1.546,3.96-2.964,6.731-3.881
							c0.004,0.38-0.004,0.751-0.023,1.116c-1.724,0.838-3.171,1.873-4.52,2.977c-1.857,1.569-3.456,3.323-4.819,5.246
							c-1.367,1.919-2.504,4.003-3.385,6.269c-0.734,1.99-1.335,4.075-1.465,6.496c-0.691,0.338-1.421,0.648-2.214,0.913
							c-0.026-2.913,0.645-5.329,1.491-7.621c0.881-2.266,2.017-4.35,3.385-6.269C375.55,376.229,377.149,374.475,379.006,372.905z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M369.753,391.777c1.394,2.558,3.155,4.343,5.008,5.934c1.872,1.552,3.878,2.82,6.006,3.833
								c2.126,1.017,4.376,1.772,6.762,2.241c2.403,0.432,4.901,0.665,7.769,0.119c-1.39-2.567-3.149-4.356-5.003-5.944
								c-1.871-1.553-3.879-2.818-6.011-3.823c-2.129-1.009-4.381-1.761-6.766-2.23C375.112,391.475,372.616,391.24,369.753,391.777
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M393.095,402.858c-1.692-0.941-3.413-1.823-5.139-2.695c-1.725-0.874-3.472-1.698-5.208-2.55
								c-1.756-0.807-3.501-1.638-5.267-2.424c-1.767-0.785-3.538-1.561-5.337-2.276c1.692,0.942,3.413,1.823,5.138,2.696
								c1.725,0.872,3.472,1.699,5.208,2.549c1.757,0.806,3.501,1.639,5.268,2.423C389.525,401.365,391.296,402.141,393.095,402.858
								z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M390.294,397.959c1.854,1.589,3.614,3.377,5.003,5.944
							c-0.374,0.071-0.741,0.128-1.102,0.174c-1.128-1.55-2.401-2.792-3.725-3.927c-1.871-1.553-3.879-2.818-6.011-3.823
							c-2.13-1.009-4.381-1.761-6.766-2.23c-2.088-0.373-4.246-0.599-6.653-0.3c-0.454-0.621-0.887-1.285-1.287-2.02
							c2.863-0.537,5.36-0.301,7.764,0.129c2.386,0.469,4.637,1.222,6.766,2.23C386.415,395.14,388.422,396.406,390.294,397.959z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M350.278,395.166c2.889-0.376,5.189-1.374,7.342-2.527c2.123-1.185,4.03-2.598,5.743-4.216
								c1.716-1.615,3.233-3.44,4.531-5.496c1.271-2.085,2.39-4.33,2.916-7.202c-2.896,0.369-5.199,1.364-7.35,2.519
								c-2.124,1.184-4.029,2.599-5.735,4.225c-1.71,1.621-3.224,3.449-4.523,5.504C351.933,390.06,350.811,392.302,350.278,395.166
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M369.041,377.401c-1.489,1.238-2.933,2.525-4.369,3.819c-1.438,1.293-2.838,2.625-4.259,3.935
								c-1.387,1.346-2.792,2.673-4.163,4.037c-1.37,1.364-2.733,2.735-4.05,4.155c1.49-1.238,2.933-2.524,4.37-3.818
								c1.436-1.294,2.838-2.624,4.258-3.936c1.386-1.347,2.793-2.673,4.162-4.038C366.36,380.191,367.723,378.82,369.041,377.401z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M363.461,378.244c2.151-1.155,4.454-2.15,7.35-2.519
							c-0.069,0.374-0.148,0.737-0.236,1.091c-1.852,0.492-3.471,1.231-5.007,2.056c-2.124,1.184-4.029,2.599-5.735,4.225
							c-1.71,1.621-3.225,3.449-4.523,5.504c-1.102,1.812-2.092,3.743-2.683,6.095c-0.743,0.199-1.519,0.363-2.348,0.471
							c0.533-2.864,1.655-5.107,2.924-7.193c1.299-2.055,2.814-3.883,4.523-5.504C359.432,380.843,361.337,379.428,363.461,378.244z
							"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M350.762,394.992c0.878,2.778,2.264,4.867,3.778,6.784c1.54,1.882,3.266,3.511,5.16,4.913
								c1.891,1.405,3.955,2.578,6.207,3.495c2.276,0.885,4.683,1.592,7.602,1.606c-0.872-2.786-2.256-4.879-3.771-6.793
								c-1.539-1.883-3.267-3.51-5.167-4.904c-1.897-1.398-3.962-2.569-6.213-3.486C356.08,395.723,353.675,395.013,350.762,394.992
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M371.547,410.341c-1.481-1.248-3.001-2.443-4.527-3.63c-1.525-1.188-3.083-2.332-4.622-3.501
								c-1.569-1.129-3.122-2.279-4.705-3.389c-1.584-1.109-3.173-2.21-4.802-3.258c1.48,1.249,3,2.444,4.526,3.631
								c1.526,1.187,3.082,2.333,4.623,3.5c1.57,1.128,3.122,2.28,4.706,3.388C368.329,408.192,369.919,409.293,371.547,410.341z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M369.737,404.996c1.516,1.915,2.899,4.007,3.771,6.793
							c-0.38-0.002-0.751-0.017-1.115-0.041c-0.81-1.737-1.821-3.201-2.904-4.568c-1.539-1.882-3.267-3.509-5.166-4.904
							c-1.897-1.398-3.962-2.568-6.213-3.486c-1.978-0.767-4.052-1.402-6.472-1.57c-0.327-0.696-0.624-1.431-0.876-2.229
							c2.913,0.022,5.318,0.732,7.595,1.615c2.252,0.918,4.317,2.088,6.213,3.486C366.47,401.487,368.198,403.114,369.737,404.996z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M333.456,395.116c2.909-0.159,5.278-0.981,7.511-1.97c2.206-1.023,4.214-2.289,6.043-3.775
								c1.832-1.482,3.481-3.189,4.93-5.142c1.424-1.984,2.707-4.139,3.447-6.963c-2.915,0.151-5.286,0.97-7.518,1.962
								c-2.206,1.022-4.212,2.29-6.035,3.783c-1.826,1.489-3.474,3.198-4.923,5.15C335.488,390.147,334.202,392.3,333.456,395.116z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M353.496,378.805c-1.578,1.123-3.113,2.298-4.643,3.481c-1.53,1.181-3.026,2.405-4.541,3.605
								c-1.484,1.238-2.984,2.457-4.453,3.714c-1.468,1.258-2.93,2.523-4.35,3.84c1.578-1.123,3.114-2.298,4.643-3.48
								c1.529-1.183,3.027-2.405,4.541-3.606c1.483-1.24,2.985-2.456,4.452-3.715C350.613,381.387,352.075,380.122,353.496,378.805z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M347.868,379.228c2.232-0.991,4.603-1.811,7.518-1.962
							c-0.096,0.368-0.203,0.723-0.317,1.07c-1.884,0.352-3.553,0.967-5.147,1.675c-2.206,1.022-4.212,2.29-6.035,3.783
							c-1.826,1.489-3.474,3.198-4.923,5.15c-1.235,1.724-2.366,3.576-3.132,5.877c-0.756,0.143-1.542,0.249-2.377,0.294
							c0.746-2.816,2.032-4.969,3.454-6.954c1.449-1.952,3.097-3.661,4.923-5.15C343.656,381.518,345.662,380.25,347.868,379.228z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M333.951,394.978c0.667,2.836,1.893,5.023,3.259,7.048c1.394,1.992,2.993,3.746,4.777,5.285
								c1.781,1.543,3.751,2.867,5.928,3.95c2.203,1.053,4.55,1.938,7.46,2.17c-0.661-2.843-1.884-5.034-3.252-7.056
								c-1.394-1.993-2.995-3.744-4.785-5.277c-1.787-1.536-3.758-2.858-5.935-3.942
								C339.199,396.106,336.854,395.218,333.951,394.978z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M353.529,411.841c-1.383-1.356-2.81-2.661-4.242-3.959c-1.432-1.299-2.899-2.557-4.347-3.837
								c-1.48-1.243-2.943-2.507-4.438-3.731c-1.496-1.224-2.998-2.442-4.545-3.608c1.382,1.356,2.809,2.662,4.241,3.96
								c1.433,1.298,2.899,2.557,4.348,3.836c1.481,1.242,2.942,2.507,4.439,3.731C350.481,409.457,351.983,410.674,353.529,411.841
								z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M352.124,406.375c1.368,2.023,2.591,4.213,3.252,7.056
							c-0.379-0.03-0.748-0.073-1.109-0.124c-0.677-1.793-1.576-3.328-2.553-4.773c-1.394-1.992-2.995-3.744-4.785-5.277
							c-1.787-1.536-3.758-2.858-5.935-3.941c-1.915-0.913-3.936-1.701-6.336-2.05c-0.274-0.719-0.515-1.474-0.707-2.288
							c2.903,0.24,5.248,1.128,7.453,2.179c2.177,1.083,4.148,2.405,5.935,3.941C349.129,402.631,350.73,404.383,352.124,406.375z"
                  />
                </g>
              </g>
              <g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M313.669,390.437c2.875,0.467,5.366,0.17,7.759-0.318c2.374-0.527,4.606-1.334,6.71-2.395
								c2.107-1.056,4.083-2.37,5.915-3.968c1.815-1.633,3.53-3.464,4.857-6.065c-2.88-0.476-5.372-0.183-7.763,0.308
								c-2.374,0.527-4.605,1.337-6.705,2.405c-2.103,1.064-4.077,2.381-5.91,3.978
								C316.718,386.018,315.001,387.846,313.669,390.437z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M336.735,378.79c-1.781,0.76-3.533,1.579-5.28,2.407c-1.748,0.827-3.471,1.702-5.207,2.551
								c-1.715,0.892-3.441,1.761-5.144,2.675c-1.703,0.915-3.402,1.838-5.071,2.821c1.782-0.759,3.533-1.578,5.28-2.406
								c1.747-0.828,3.471-1.701,5.207-2.551c1.714-0.894,3.441-1.761,5.144-2.676C333.367,380.696,335.066,379.773,336.735,378.79z
								"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M331.147,378c2.392-0.491,4.883-0.784,7.763-0.308
							c-0.173,0.339-0.353,0.663-0.538,0.977c-1.915-0.059-3.678,0.185-5.386,0.535c-2.374,0.526-4.605,1.336-6.705,2.405
							c-2.103,1.064-4.077,2.381-5.911,3.978c-1.575,1.421-3.076,2.987-4.316,5.071c-0.769-0.022-1.559-0.087-2.385-0.221
							c1.331-2.591,3.048-4.419,4.862-6.054c1.833-1.597,3.808-2.914,5.91-3.978C326.542,379.336,328.773,378.526,331.147,378z"
                  />
                </g>
                <g>
                  <g>
                    <path
                      style={{ fill: "#6E7FDD" }}
                      d="M314.183,390.408c0.045,2.913,0.775,5.312,1.676,7.582c0.936,2.244,2.123,4.3,3.536,6.185
								c1.41,1.888,3.051,3.603,4.946,5.126c1.927,1.5,4.031,2.867,6.823,3.716c-0.038-2.919-0.764-5.32-1.667-7.589
								c-0.935-2.244-2.125-4.298-3.545-6.178c-1.417-1.883-3.06-3.596-4.954-5.12C319.069,392.633,316.968,391.264,314.183,390.408
								z"
                    />
                    <path
                      style={{ fill: "#0046A0" }}
                      d="M329.701,411.069c-1.061-1.62-2.175-3.201-3.297-4.775c-1.121-1.575-2.285-3.118-3.426-4.678
								c-1.18-1.531-2.338-3.078-3.538-4.594c-1.2-1.516-2.407-3.026-3.668-4.496c1.06,1.621,2.174,3.201,3.296,4.776
								c1.122,1.574,2.285,3.118,3.427,4.678c1.181,1.53,2.338,3.078,3.538,4.594C327.233,408.088,328.44,409.598,329.701,411.069z"
                    />
                  </g>
                  <path
                    style={{ opacity: "0.1", fill: "#0046A0" }}
                    d="M329.497,405.429c0.904,2.268,1.63,4.67,1.667,7.589
							c-0.364-0.111-0.715-0.231-1.057-0.358c-0.278-1.896-0.828-3.588-1.473-5.208c-0.935-2.244-2.125-4.298-3.545-6.178
							c-1.417-1.883-3.06-3.596-4.954-5.12c-1.675-1.301-3.481-2.504-5.751-3.358c-0.114-0.76-0.188-1.55-0.201-2.386
							c2.785,0.855,4.886,2.224,6.814,3.723c1.894,1.524,3.538,3.237,4.954,5.12C327.373,401.131,328.562,403.184,329.497,405.429z"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
        <g>
          <path
            style={{ fill: "#6E7FDD" }}
            d="M338.29,405.514c0,7.802-6.383,14.185-14.185,14.185H164.414c-7.802,0-14.185-6.383-14.185-14.185
			V123.808c0-7.802,6.383-14.185,14.185-14.185h159.691c7.802,0,14.185,6.383,14.185,14.185V405.514z"
          />
          <path
            style={{ fill: "#C2CEF2" }}
            d="M281.599,120.945c-1.585,5.572-17.691,9.951-37.339,9.951s-35.755-4.379-37.34-9.951h-42.506
			v267.238h159.691V120.945H281.599z"
          />
          <g>
            <g>
              <g>
                <path
                  style={{ fill: "#B0C1EA" }}
                  d="M303.434,168.76H184.48c-1.84,0-3.345,1.505-3.345,3.345v26.691v4.768v10.604l13.336-7.259
						h108.962c1.84,0,3.345-1.505,3.345-3.345v-31.459C306.778,170.266,305.273,168.76,303.434,168.76z"
                />
                <g>
                  <g>
                    <rect
                      x="191.883"
                      y="176.684"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="191.883"
                      y="187.832"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="191.883"
                      y="198.981"
                      style={{ fill: "#D2DDF9" }}
                      width="74.011"
                      height="1.115"
                    />
                  </g>
                </g>
              </g>
              <g>
                <circle
                  style={{ fill: "#B0C1EA" }}
                  cx="192.754"
                  cy="152.578"
                  r="11.352"
                />
                <g>
                  <defs>
                    <circle
                      id="SVGID_1_"
                      cx="192.754"
                      cy="152.578"
                      r="11.352"
                    />
                  </defs>
                  <clipPath id="SVGID_00000042718052710388624010000002382202384989819277_">
                    <use
                      xlinkHref="#SVGID_1_"
                      style={{ overflow: "visible" }}
                    />
                  </clipPath>
                  <g
                    style={{
                      clipPath: "url(#SVGID_00000042718052710388624010000002382202384989819277_)",
                    }}
                  >
                    <circle
                      style={{ fill: "#D2DDF9" }}
                      cx="192.754"
                      cy="150.804"
                      r="3.104"
                    />
                    <path
                      style={{ fill: "#D2DDF9" }}
                      d="M192.754,155.061c-3.478,0-6.297,2.819-6.297,6.297s2.819,6.297,6.297,6.297
								s6.297-2.819,6.297-6.297S196.231,155.061,192.754,155.061z"
                    />
                  </g>
                </g>
              </g>
            </g>
            <g>
              <g>
                <path
                  style={{ fill: "#B0C1EA" }}
                  d="M184.48,246.803h118.953c1.84,0,3.345,1.505,3.345,3.345v26.691v4.768v10.604l-13.336-7.259
						H184.48c-1.84,0-3.345-1.505-3.345-3.345v-31.459C181.136,248.308,182.641,246.803,184.48,246.803z"
                />
                <g>
                  <g>
                    <rect
                      x="191.883"
                      y="254.726"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="191.883"
                      y="265.875"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="222.021"
                      y="277.024"
                      style={{ fill: "#D2DDF9" }}
                      width="74.011"
                      height="1.115"
                    />
                  </g>
                </g>
              </g>
              <g>
                <circle
                  style={{ fill: "#B0C1EA" }}
                  cx="296.031"
                  cy="229.784"
                  r="11.352"
                />
                <g>
                  <defs>
                    <circle
                      id="SVGID_00000011748870046927669070000016059859782713271464_"
                      cx="296.031"
                      cy="229.784"
                      r="11.352"
                    />
                  </defs>
                  <clipPath id="SVGID_00000020395922392201483360000013388271209222066579_">
                    <use
                      xlinkHref="#SVGID_00000011748870046927669070000016059859782713271464_"
                      style={{ overflow: "visible" }}
                    />
                  </clipPath>
                  <g
                    style={{
                      clipPath: "url(#SVGID_00000020395922392201483360000013388271209222066579_)",
                    }}
                  >
                    <path
                      style={{ fill: "#D2DDF9" }}
                      d="M296.031,231.115c1.714,0,3.104-1.39,3.104-3.104c0-1.714-1.39-3.104-3.104-3.104
								c-1.714,0-3.104,1.39-3.104,3.104C292.927,229.725,294.317,231.115,296.031,231.115z"
                    />
                    <path
                      style={{ fill: "#D2DDF9" }}
                      d="M296.031,232.268c-3.478,0-6.297,2.819-6.297,6.297c0,3.477,2.819,6.297,6.297,6.297
								c3.478,0,6.297-2.819,6.297-6.297C302.328,235.087,299.509,232.268,296.031,232.268z"
                    />
                  </g>
                </g>
              </g>
            </g>
            <g>
              <g>
                <path
                  style={{ fill: "#B0C1EA" }}
                  d="M303.434,327.075H184.48c-1.84,0-3.345,1.505-3.345,3.345v26.691v4.768v10.604l13.336-7.259
						h108.962c1.84,0,3.345-1.505,3.345-3.345v-31.459C306.778,328.58,305.273,327.075,303.434,327.075z"
                />
                <g>
                  <g>
                    <rect
                      x="191.883"
                      y="334.998"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="191.883"
                      y="346.147"
                      style={{ fill: "#D2DDF9" }}
                      width="104.149"
                      height="1.115"
                    />
                  </g>
                  <g>
                    <rect
                      x="191.883"
                      y="357.296"
                      style={{ fill: "#D2DDF9" }}
                      width="74.011"
                      height="1.115"
                    />
                  </g>
                </g>
              </g>
              <g>
                <circle
                  style={{ fill: "#B0C1EA" }}
                  cx="192.754"
                  cy="309.397"
                  r="11.352"
                />
                <g>
                  <defs>
                    <circle
                      id="SVGID_00000096059885508084991210000017981738161227755683_"
                      cx="192.754"
                      cy="309.397"
                      r="11.352"
                    />
                  </defs>
                  <clipPath id="SVGID_00000036243940869179399690000007258308252422043014_">
                    <use
                      xlinkHref="#SVGID_00000096059885508084991210000017981738161227755683_"
                      style={{ overflow: "visible" }}
                    />
                  </clipPath>
                  <g
                    style={{
                      clipPath: "url(#SVGID_00000036243940869179399690000007258308252422043014_)",
                    }}
                  >
                    <path
                      style={{ fill: "#D2DDF9" }}
                      d="M192.754,310.728c1.714,0,3.104-1.39,3.104-3.104c0-1.714-1.39-3.104-3.104-3.104
								c-1.714,0-3.104,1.39-3.104,3.104C189.65,309.338,191.039,310.728,192.754,310.728z"
                    />
                    <path
                      style={{ fill: "#D2DDF9" }}
                      d="M192.754,311.881c-3.478,0-6.297,2.819-6.297,6.297c0,3.478,2.819,6.297,6.297,6.297
								s6.297-2.819,6.297-6.297C199.05,314.7,196.231,311.881,192.754,311.881z"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
          <polygon
            style={{ fill: "#6E7FDD" }}
            points="295.287,229.203 280.261,214.176 244.259,250.176 208.258,214.176 193.231,229.203 
			229.233,265.202 193.231,301.202 208.258,316.228 244.259,280.229 280.261,316.228 295.287,301.202 259.286,265.202 		"
          />
        </g>
        <g>
          <g>
            <rect
              x="284.683"
              y="91.586"
              style={{ fill: "#F2F2F2" }}
              width="132.602"
              height="99.931"
            />
            <rect
              x="292.441"
              y="97.882"
              style={{ fill: "#D2DDF7" }}
              width="117.087"
              height="15.965"
            />
            <polygon
              style={{ fill: "#F2F2F2" }}
              points="405.713,101.468 403.942,99.697 399.698,103.94 395.454,99.697 393.683,101.468 
				397.927,105.712 393.683,109.955 395.454,111.727 399.698,107.483 403.942,111.727 405.713,109.955 401.47,105.712 			"
            />
          </g>
          <g>
            <g>
              <g>
                <path
                  style={{ fill: "#6E7FDD" }}
                  d="M350.595,173.202c-0.021,0-0.04,0-0.061,0c-8.845-0.04-17.002-1.358-24.946-4.031
						c-4.122-1.362-8.326-3.225-12.488-5.535c-1.867-1.073-4.049-2.373-6.278-3.989c-2.101-1.464-4.229-3.232-6.294-5.236
						l-1.422-1.379l1.422-1.379c2.067-2.004,4.194-3.772,6.324-5.256c2.2-1.595,4.383-2.895,6.224-3.953
						c4.187-2.324,8.391-4.188,12.522-5.553c7.933-2.669,16.091-3.987,24.929-4.028c0.02,0,0.04,0,0.061,0
						c8.759,0,16.899,1.301,24.891,3.978c4.138,1.366,8.343,3.233,12.493,5.544c1.877,1.085,4.067,2.396,6.279,4.006
						c2.094,1.464,4.221,3.243,6.294,5.265l1.41,1.375l-1.41,1.375c-2.071,2.022-4.199,3.801-6.323,5.287
						c-2.183,1.59-4.373,2.9-6.224,3.971c-4.178,2.327-8.383,4.193-12.527,5.561C367.489,171.9,359.349,173.202,350.595,173.202z"
                />
              </g>
              <path
                style={{ fill: "#FFCF74" }}
                d="M359.715,154.834c-6.067,0-10.985-4.918-10.985-10.985c0-1.514,0.306-2.956,0.861-4.269
					c-7.016,0.485-12.56,6.314-12.56,13.453c0,7.456,6.044,13.5,13.501,13.5c7.139,0,12.968-5.545,13.453-12.56
					C362.671,154.527,361.229,154.834,359.715,154.834z"
              />
            </g>
            <g>
              <rect
                x="317.02"
                y="151.477"
                transform="matrix(0.7071 -0.7071 0.7071 0.7071 -5.5396 292.6928)"
                style={{ fill: "#EB725F" }}
                width="67.02"
                height="3.112"
              />
            </g>
          </g>
        </g>
        <g>
          <path
            style={{ fill: "#FFCF74" }}
            d="M162.049,212.002c-25.197,0-45.697,20.499-45.697,45.696c0,25.197,20.499,45.697,45.697,45.697
			c25.197,0,45.697-20.5,45.697-45.697C207.746,232.501,187.247,212.002,162.049,212.002z"
          />
          <path
            style={{ fill: "#FFFFFF" }}
            d="M162.049,226.106c-17.42,0-31.593,14.172-31.593,31.592c0,17.42,14.172,31.593,31.593,31.593
			c17.421,0,31.593-14.173,31.593-31.593C193.643,240.278,179.47,226.106,162.049,226.106z M162.049,232.063
			c5.997,0,11.514,2.075,15.885,5.537l-35.983,35.983c-3.462-4.371-5.537-9.889-5.537-15.885
			C136.414,243.563,147.914,232.063,162.049,232.063z M162.049,283.334c-5.997,0-11.514-2.075-15.886-5.538l35.984-35.984
			c3.463,4.371,5.538,9.889,5.538,15.886C187.685,271.834,176.185,283.334,162.049,283.334z"
          />
        </g>
        <g>
          <path
            style={{ fill: "#F9AB43" }}
            d="M154.738,168.838h-4.37c-0.105-0.461-0.216-0.919-0.352-1.368l3.781-2.166
			c1.939-1.111,2.617-3.606,1.506-5.545l-0.457-0.798c-1.111-1.939-3.606-2.617-5.545-1.506l-3.8,2.177
			c-0.365-0.395-0.744-0.774-1.139-1.139l2.177-3.801c1.111-1.939,0.433-4.434-1.506-5.544l-0.798-0.457
			c-1.939-1.111-4.434-0.433-5.544,1.506l-2.166,3.781c-0.448-0.136-0.907-0.246-1.367-0.352v-4.37c0-2.234-1.828-4.063-4.063-4.063
			h-0.92c-2.234,0-4.063,1.828-4.063,4.063v4.37c-0.461,0.105-0.919,0.216-1.368,0.352l-2.166-3.781
			c-1.111-1.939-3.606-2.617-5.544-1.506l-0.799,0.457c-1.939,1.111-2.617,3.606-1.506,5.544l2.177,3.801
			c-0.395,0.365-0.774,0.744-1.139,1.139l-3.801-2.177c-1.939-1.111-4.434-0.433-5.544,1.506l-0.457,0.798
			c-1.111,1.939-0.433,4.434,1.506,5.545l3.782,2.166c-0.136,0.448-0.247,0.907-0.352,1.367h-4.37c-2.234,0-4.063,1.828-4.063,4.063
			v0.92c0,2.234,1.828,4.063,4.063,4.063h4.37c0.105,0.461,0.216,0.919,0.352,1.368l-3.782,2.166
			c-1.939,1.111-2.617,3.606-1.506,5.545l0.457,0.798c1.111,1.939,3.606,2.617,5.544,1.506l3.801-2.177
			c0.365,0.395,0.744,0.774,1.139,1.139l-2.177,3.801c-1.111,1.939-0.433,4.434,1.506,5.545l0.799,0.457
			c1.939,1.111,4.434,0.433,5.544-1.506l2.166-3.782c0.448,0.136,0.907,0.246,1.368,0.352v4.37c0,2.234,1.828,4.063,4.063,4.063
			h0.92c2.234,0,4.063-1.828,4.063-4.063v-4.37c0.461-0.105,0.919-0.216,1.367-0.352l2.166,3.782
			c1.111,1.939,3.606,2.617,5.544,1.506l0.798-0.457c1.939-1.111,2.617-3.606,1.506-5.545l-2.177-3.801
			c0.395-0.365,0.774-0.744,1.139-1.139l3.801,2.177c1.939,1.111,4.434,0.433,5.545-1.506l0.457-0.798
			c1.111-1.939,0.433-4.434-1.506-5.545l-3.782-2.166c0.136-0.448,0.247-0.907,0.352-1.368h4.37c2.234,0,4.063-1.828,4.063-4.063
			v-0.92C158.8,170.666,156.972,168.838,154.738,168.838z M130.634,187.305c-7.701,0-13.945-6.243-13.945-13.945
			c0-7.701,6.243-13.944,13.945-13.944c7.701,0,13.945,6.243,13.945,13.944C144.578,181.062,138.335,187.305,130.634,187.305z"
          />
          <path
            style={{ fill: "#EB725F" }}
            d="M121.574,120.895l-2.887,0.656c-0.139-0.289-0.28-0.575-0.438-0.851l2.173-1.999
			c1.114-1.025,1.188-2.775,0.163-3.889l-0.422-0.459c-1.025-1.114-2.775-1.188-3.889-0.163l-2.184,2.009
			c-0.3-0.206-0.608-0.4-0.923-0.581l0.868-2.838c0.443-1.448-0.379-2.995-1.827-3.437l-0.596-0.182
			c-1.448-0.443-2.994,0.379-3.437,1.827l-0.864,2.824c-0.316-0.023-0.636-0.027-0.956-0.027l-0.656-2.887
			c-0.335-1.476-1.818-2.41-3.294-2.074l-0.608,0.138c-1.476,0.335-2.41,1.818-2.074,3.294l0.656,2.887
			c-0.289,0.139-0.575,0.281-0.851,0.438l-1.999-2.173c-1.025-1.114-2.775-1.188-3.889-0.163l-0.459,0.422
			c-1.114,1.025-1.188,2.775-0.163,3.889l2.009,2.184c-0.206,0.3-0.4,0.608-0.581,0.923l-2.838-0.868
			c-1.448-0.443-2.994,0.38-3.437,1.827l-0.182,0.596c-0.443,1.448,0.379,2.995,1.827,3.437l2.824,0.864
			c-0.023,0.316-0.027,0.636-0.027,0.956l-2.887,0.656c-1.476,0.335-2.41,1.818-2.074,3.294l0.138,0.608
			c0.335,1.476,1.818,2.41,3.294,2.074l2.887-0.656c0.139,0.289,0.28,0.575,0.438,0.851l-2.173,1.999
			c-1.114,1.025-1.188,2.775-0.163,3.889l0.422,0.459c1.025,1.114,2.775,1.188,3.889,0.163l2.184-2.009
			c0.3,0.206,0.608,0.4,0.923,0.581l-0.868,2.838c-0.443,1.448,0.379,2.995,1.827,3.437l0.596,0.182
			c1.448,0.443,2.994-0.379,3.437-1.827l0.864-2.824c0.316,0.023,0.636,0.027,0.956,0.027l0.656,2.887
			c0.335,1.476,1.818,2.41,3.294,2.074l0.608-0.138c1.476-0.335,2.41-1.818,2.074-3.294l-0.656-2.887
			c0.289-0.139,0.575-0.281,0.851-0.438l1.999,2.173c1.025,1.114,2.775,1.188,3.889,0.163l0.459-0.422
			c1.114-1.025,1.188-2.775,0.163-3.889l-2.009-2.184c0.206-0.3,0.4-0.608,0.581-0.923l2.838,0.868
			c1.448,0.443,2.994-0.379,3.437-1.827l0.182-0.596c0.443-1.448-0.379-2.995-1.827-3.437l-2.824-0.864
			c0.023-0.316,0.027-0.636,0.027-0.956l2.887-0.656c1.476-0.335,2.41-1.818,2.074-3.294l-0.138-0.608
			C124.532,121.493,123.05,120.56,121.574,120.895z M108.42,136.714c-5.088,1.156-10.15-2.032-11.306-7.12
			c-1.156-5.088,2.032-10.15,7.12-11.306c5.088-1.156,10.15,2.032,11.306,7.12C116.696,130.496,113.508,135.558,108.42,136.714z"
          />
          <path
            style={{ fill: "#EB725F" }}
            d="M385.939,195.574l-2.837,0.644c-0.136-0.283-0.276-0.565-0.43-0.836l2.135-1.964
			c1.095-1.007,1.167-2.726,0.16-3.821l-0.415-0.451c-1.007-1.095-2.726-1.167-3.821-0.16l-2.146,1.973
			c-0.295-0.202-0.597-0.393-0.907-0.571l0.853-2.788c0.435-1.422-0.373-2.942-1.795-3.377l-0.586-0.179
			c-1.422-0.435-2.942,0.373-3.377,1.795l-0.848,2.774c-0.311-0.022-0.625-0.026-0.94-0.027l-0.644-2.837
			c-0.329-1.45-1.786-2.367-3.236-2.038l-0.597,0.136c-1.45,0.329-2.367,1.786-2.038,3.236l0.644,2.837
			c-0.284,0.136-0.565,0.275-0.836,0.43l-1.964-2.135c-1.007-1.095-2.726-1.167-3.821-0.16l-0.451,0.415
			c-1.095,1.007-1.167,2.726-0.16,3.821l1.974,2.146c-0.202,0.295-0.393,0.597-0.571,0.907l-2.788-0.853
			c-1.422-0.435-2.942,0.373-3.377,1.795l-0.179,0.586c-0.435,1.422,0.373,2.942,1.795,3.377l2.774,0.848
			c-0.022,0.311-0.026,0.625-0.027,0.939l-2.837,0.645c-1.45,0.329-2.367,1.786-2.038,3.236l0.136,0.597
			c0.329,1.45,1.786,2.367,3.236,2.038l2.837-0.645c0.136,0.284,0.275,0.565,0.43,0.836l-2.135,1.964
			c-1.095,1.007-1.167,2.726-0.16,3.821l0.415,0.451c1.007,1.095,2.726,1.167,3.821,0.16l2.146-1.974
			c0.295,0.202,0.597,0.393,0.907,0.571l-0.853,2.788c-0.435,1.422,0.373,2.942,1.795,3.377l0.586,0.179
			c1.422,0.435,2.942-0.373,3.377-1.795l0.848-2.774c0.311,0.022,0.625,0.026,0.939,0.027l0.644,2.837
			c0.329,1.45,1.786,2.367,3.236,2.038l0.597-0.136c1.45-0.329,2.367-1.786,2.038-3.236l-0.644-2.837
			c0.283-0.136,0.565-0.276,0.836-0.43l1.964,2.135c1.007,1.095,2.726,1.167,3.821,0.16l0.451-0.415
			c1.095-1.007,1.167-2.726,0.16-3.821l-1.973-2.146c0.202-0.295,0.393-0.597,0.571-0.907l2.788,0.853
			c1.422,0.435,2.942-0.373,3.377-1.795l0.179-0.586c0.435-1.422-0.373-2.942-1.795-3.377l-2.774-0.848
			c0.022-0.311,0.026-0.625,0.027-0.94l2.837-0.644c1.45-0.329,2.367-1.786,2.038-3.236l-0.136-0.597
			C388.846,196.161,387.39,195.244,385.939,195.574z M373.017,211.115c-4.999,1.136-9.972-1.996-11.107-6.995
			c-1.136-4.999,1.996-9.972,6.995-11.107c4.999-1.136,9.972,1.996,11.107,6.995S378.016,209.979,373.017,211.115z"
          />
        </g>
        <g>
          <g>
            <g>
              <rect
                x="246.539"
                y="419.699"
                style={{ fill: "#FFCF74" }}
                width="135.37"
                height="7.702"
              />
              <g>
                <g>
                  <polygon
                    style={{ fill: "#F9AB43" }}
                    points="368.918,419.699 259.53,419.699 306.812,303.107 321.636,303.107 						"
                  />
                  <g>
                    <defs>
                      <polygon
                        id="SVGID_00000108269280336561762060000014581223070438149270_"
                        points="368.918,419.699 259.53,419.699 
									306.812,303.107 321.636,303.107 								"
                      />
                    </defs>
                    <clipPath id="SVGID_00000080165414059899668970000004949660428773930395_">
                      <use
                        xlinkHref="#SVGID_00000108269280336561762060000014581223070438149270_"
                        style={{ overflow: "visible" }}
                      />
                    </clipPath>
                    <g
                      style={{
                        clipPath: "url(#SVGID_00000080165414059899668970000004949660428773930395_)",
                      }}
                    >
                      <rect
                        x="239.752"
                        y="376.85"
                        style={{ fill: "#FFCF74" }}
                        width="148.944"
                        height="27.948"
                      />
                      <rect
                        x="239.752"
                        y="321.293"
                        style={{ fill: "#FFCF74" }}
                        width="148.944"
                        height="27.948"
                      />
                    </g>
                  </g>
                </g>
                <path
                  style={{ opacity: "0.1", fill: "#0046A0" }}
                  d="M331.764,344.816c8.808,26.774,9.614,53.388,3.815,74.883h33.339l-47.283-116.592
						H310.34C319.136,314.873,326.559,328.992,331.764,344.816z"
                />
              </g>
            </g>
            <g style={{ opacity: "0.3" }}>
              <path
                style={{ fill: "#FFFFFF" }}
                d="M304.76,326.486c-3.067,8.066-6.134,16.132-9.2,24.198c-4.894,12.872-9.788,25.744-14.682,38.616
					c-1.131,2.976-2.263,5.951-3.394,8.927c-1.015,2.669,3.269,3.823,4.274,1.178c3.067-8.066,6.134-16.132,9.2-24.198
					c4.894-12.872,9.788-25.744,14.682-38.616c1.131-2.976,2.263-5.951,3.394-8.927C310.05,324.995,305.766,323.841,304.76,326.486
					L304.76,326.486z"
              />
            </g>
          </g>
          <g>
            <g>
              <rect
                x="110.431"
                y="422.902"
                style={{ fill: "#FFCF74" }}
                width="79.071"
                height="4.499"
              />
              <g>
                <g>
                  <polygon
                    style={{ fill: "#F9AB43" }}
                    points="181.914,422.902 118.019,422.902 145.637,354.799 154.296,354.799 						"
                  />
                  <g>
                    <defs>
                      <polygon
                        id="SVGID_00000082349415791018970090000006782770546947194287_"
                        points="181.914,422.902 118.019,422.902 
									145.637,354.799 154.296,354.799 								"
                      />
                    </defs>
                    <clipPath id="SVGID_00000172421010732287868150000010766578436945899193_">
                      <use
                        xlinkHref="#SVGID_00000082349415791018970090000006782770546947194287_"
                        style={{ overflow: "visible" }}
                      />
                    </clipPath>
                    <g
                      style={{
                        clipPath: "url(#SVGID_00000172421010732287868150000010766578436945899193_)",
                      }}
                    >
                      <rect
                        x="106.466"
                        y="397.874"
                        style={{ fill: "#FFCF74" }}
                        width={87}
                        height="16.324"
                      />
                      <rect
                        x="106.466"
                        y="365.422"
                        style={{ fill: "#FFCF74" }}
                        width={87}
                        height="16.324"
                      />
                    </g>
                  </g>
                </g>
                <path
                  style={{ opacity: "0.1", fill: "#0046A0" }}
                  d="M160.212,379.162c5.145,15.639,5.615,31.184,2.229,43.74h19.474l-27.618-68.103
						h-6.598C152.835,361.672,157.171,369.919,160.212,379.162z"
                />
              </g>
            </g>
            <g style={{ opacity: "0.3" }}>
              <path
                style={{ fill: "#FFFFFF" }}
                d="M144.438,368.455c-1.791,4.711-3.583,9.423-5.374,14.134c-2.859,7.519-5.717,15.037-8.576,22.556
					c-0.661,1.738-1.322,3.476-1.982,5.214c-0.593,1.559,1.909,2.233,2.497,0.688c1.791-4.711,3.583-9.423,5.374-14.134
					c2.859-7.519,5.717-15.037,8.576-22.556c0.661-1.738,1.322-3.476,1.983-5.214C147.528,367.585,145.026,366.91,144.438,368.455
					L144.438,368.455z"
              />
            </g>
          </g>
        </g>
        <g>
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="369.14,244.886 361.601,244.886 361.601,237.347 358.917,237.347 358.917,244.886 
			351.379,244.886 351.379,247.569 358.917,247.569 358.917,255.107 361.601,255.107 361.601,247.569 369.14,247.569 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="415.065,320.905 408.46,320.905 408.46,314.3 406.11,314.3 406.11,320.905 
			399.505,320.905 399.505,323.256 406.11,323.256 406.11,329.86 408.46,329.86 408.46,323.256 415.065,323.256 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="419.732,266.147 415.961,266.147 415.961,262.376 414.619,262.376 414.619,266.147 
			410.849,266.147 410.849,267.489 414.619,267.489 414.619,271.26 415.961,271.26 415.961,267.489 419.732,267.489 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="263.468,78.313 259.697,78.313 259.697,74.542 258.355,74.542 258.355,78.313 
			254.584,78.313 254.584,79.655 258.355,79.655 258.355,83.425 259.697,83.425 259.697,79.655 263.468,79.655 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="293.56,64.353 286.021,64.353 286.021,56.814 283.337,56.814 283.337,64.353 
			275.799,64.353 275.799,67.036 283.337,67.036 283.337,74.574 286.021,74.574 286.021,67.036 293.56,67.036 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="102.04,229.742 95.435,229.742 95.435,223.138 93.085,223.138 93.085,229.742 
			86.48,229.742 86.48,232.093 93.085,232.093 93.085,238.697 95.435,238.697 95.435,232.093 102.04,232.093 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="75.706,171.605 71.935,171.605 71.935,167.835 70.592,167.835 70.592,171.605 
			66.822,171.605 66.822,172.947 70.592,172.947 70.592,176.718 71.935,176.718 71.935,172.947 75.706,172.947 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="71.264,330.856 67.493,330.856 67.493,327.086 66.151,327.086 66.151,330.856 
			62.38,330.856 62.38,332.198 66.151,332.198 66.151,335.969 67.493,335.969 67.493,332.198 71.264,332.198 		"
          />
          <polygon
            style={{ fill: "#CAD2F9" }}
            points="149.268,93.422 142.663,93.422 142.663,86.817 140.312,86.817 140.312,93.422 
			133.708,93.422 133.708,95.773 140.312,95.773 140.312,102.377 142.663,102.377 142.663,95.773 149.268,95.773 		"
          />
        </g>
        <g>
          <path
            style={{ fill: "#E2E6FF" }}
            d="M224.108,87.127c0,1.153-0.935,2.088-2.088,2.088c-1.153,0-2.088-0.935-2.088-2.088
			c0-1.153,0.935-2.088,2.088-2.088C223.174,85.04,224.108,85.974,224.108,87.127z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M410.849,240.85c0,2.184-1.77,3.954-3.954,3.954c-2.184,0-3.954-1.77-3.954-3.954
			c0-2.184,1.77-3.954,3.954-3.954C409.079,236.897,410.849,238.667,410.849,240.85z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M59.74,226.106c0,1.153-0.935,2.088-2.088,2.088c-1.153,0-2.088-0.935-2.088-2.088
			c0-1.153,0.935-2.088,2.088-2.088C58.806,224.018,59.74,224.953,59.74,226.106z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M181.317,68.364c0,2.183-1.77,3.954-3.954,3.954c-2.184,0-3.954-1.77-3.954-3.954
			c0-2.184,1.77-3.954,3.954-3.954C179.547,64.41,181.317,66.18,181.317,68.364z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M64.468,122.565c0,1.153-0.935,2.088-2.088,2.088s-2.088-0.935-2.088-2.088
			c0-1.153,0.935-2.088,2.088-2.088S64.468,121.412,64.468,122.565z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M75.217,393.647c0,2.184-1.77,3.954-3.954,3.954c-2.183,0-3.954-1.77-3.954-3.954
			c0-2.184,1.77-3.954,3.954-3.954C73.447,389.693,75.217,391.463,75.217,393.647z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M358.242,282.378c0,1.153-0.935,2.088-2.088,2.088c-1.153,0-2.088-0.935-2.088-2.088
			c0-1.153,0.935-2.088,2.088-2.088C357.307,280.29,358.242,281.225,358.242,282.378z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M362.347,64.41c0,1.153-0.935,2.088-2.088,2.088c-1.153,0-2.088-0.935-2.088-2.088
			c0-1.153,0.935-2.088,2.088-2.088C361.413,62.322,362.347,63.257,362.347,64.41z"
          />
          <path
            style={{ fill: "#E2E6FF" }}
            d="M87.522,269.507c0,2.184-1.77,3.954-3.954,3.954c-2.184,0-3.954-1.77-3.954-3.954
			s1.77-3.954,3.954-3.954C85.752,265.553,87.522,267.323,87.522,269.507z"
          />
        </g>
        <g>
          <path
            style={{ fill: "#FFCF74" }}
            d="M196.366,103.392c-0.462,0.709-0.469,1.119-0.444,1.191c0.039,0.045,0.386,0.265,1.233,0.265
			h42.335c0.846,0,1.193-0.22,1.24-0.279c0.016-0.057,0.01-0.469-0.452-1.177l-20.62-31.592c-0.414-0.634-0.913-1.013-1.335-1.013
			s-0.921,0.379-1.335,1.014L196.366,103.392z"
          />
          <g>
            <path
              style={{ fill: "#F9AB43" }}
              d="M239.489,109.324h-42.335c-2.387,0-4.272-0.954-5.173-2.617c-0.901-1.663-0.669-3.763,0.636-5.762
				v0l20.621-31.592c1.263-1.934,3.116-3.044,5.084-3.044s3.822,1.109,5.084,3.043l20.621,31.593
				c1.304,1.999,1.536,4.099,0.636,5.762C243.761,108.37,241.876,109.324,239.489,109.324z M196.366,103.392
				c-0.462,0.709-0.469,1.119-0.444,1.191c0.039,0.045,0.386,0.265,1.233,0.265h42.335c0.846,0,1.193-0.22,1.24-0.279
				c0.016-0.057,0.01-0.469-0.452-1.177l-20.62-31.592c-0.414-0.634-0.913-1.013-1.335-1.013s-0.921,0.379-1.335,1.014
				L196.366,103.392z"
            />
          </g>
          <g>
            <path
              style={{ fill: "#FFFFFF" }}
              d="M217.126,100.077c-0.315-0.315-0.472-0.704-0.472-1.167c0-0.463,0.157-0.852,0.472-1.167
				c0.315-0.315,0.704-0.473,1.167-0.473c0.463,0,0.857,0.158,1.181,0.473c0.323,0.315,0.486,0.704,0.486,1.167
				c0,0.463-0.163,0.852-0.486,1.167c-0.325,0.315-0.718,0.472-1.181,0.472C217.83,100.549,217.442,100.391,217.126,100.077z
				 M216.154,80.71h4.334l-0.805,13.865h-2.724L216.154,80.71z"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}
