import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Footer, DialogHeader } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { DialogProps, DialogTitle } from "@radix-ui/react-dialog";
import { convertToIng } from "@/utlis/helpers";
import ButtonLoadingContent from "./ButtonLoadingContent";

type TVariant = "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";

type AddAdminModalProps = {
  open: boolean;
  // eslint-disable-next-line no-unused-vars
  setOpen: (open: boolean) => void;
  children: React.ReactNode;
  modalTitle: string;
  modalSize?: string;
  handlePrimaryAction: () => void;
  primaryActionText: string;
  handleSecondaryAction?: () => void;
  secondaryActionText?: string;
  loading?: boolean;
  primaryVariant?: TVariant;
  secondaryVariant?: TVariant;
  // buttonProps?: Record<string, unknown>;
  disabled?: boolean;
} & DialogProps;

export default function Modal({
  open,
  setOpen,
  children,
  modalTitle,
  handlePrimaryAction,
  modalSize = "md",
  primaryActionText,
  secondaryActionText = "Cancel",
  handleSecondaryAction,
  loading = false,
  primaryVariant = "default",
  secondaryVariant = "default",
  disabled = false,
  // buttonProps = {},
  ...rest
}: AddAdminModalProps) {
  if (modalSize === "sm") {
    modalSize = "w-full sm:max-w-sm";
  } else if (modalSize === "md") {
    modalSize = "w-full md:max-w-[600px]";
  } else if (modalSize === "lg") {
    modalSize = "w-full lg:max-w-[900px]";
  } else {
    modalSize = "";
  }

  return (
    <Dialog
      {...rest}
      open={open}
      onOpenChange={setOpen}
    >
      <DialogContent className={`${modalSize} p-0`}>
        <DialogHeader className="p-3 pb-0">
          <DialogTitle className="text-md font-medium">{modalTitle}</DialogTitle>
        </DialogHeader>
        <Separator className="p-0" />
        {/* <DialogDescription className="px-3 modal-content overflow-auto">{children}</DialogDescription> */}
        <div className="px-3 modal-content overflow-auto">{children}</div>
        <Separator className="p-0" />
        <DialogFooter className="p-3 pt-0">
          {handleSecondaryAction && (
            <Button
              type="submit"
              size="sm"
              variant={secondaryVariant}
              onClick={handleSecondaryAction}
            >
              {loading ? <ButtonLoadingContent /> : secondaryActionText}
            </Button>
          )}
          <Button
            type="submit"
            size="sm"
            variant={primaryVariant}
            onClick={handlePrimaryAction}
            disabled={disabled}
          >
            {loading ? <ButtonLoadingContent label={convertToIng(primaryActionText)} /> : primaryActionText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
