import { But<PERSON> } from "@/components/ui/button";
import { Command, CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@radix-ui/react-label";
import { ChevronDown } from "lucide-react";
import { useState } from "react";

type TDropdownWrapper = {
  label?: string;
  required?: boolean;
  title: string;
  field: any;
  items: TDropdownWrapperItems[];
  disabled?: boolean;
  error?: string;
};

type TDropdownWrapperItems = {
  label: string;
  value: string | number;
};

export default function DropdownWrapper({
  title,
  field,
  items,
  disabled = false,
  error,
  label = "",
  required = true,
}: TDropdownWrapper) {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Popover
        open={open}
        onOpenChange={setOpen}
        modal={true}
      >
        <Label className="text-[13px] text-gray-500 font-normal block mb-1">
          {label}
          {required && <span className="text-red-500"> *</span>}
          <PopoverTrigger
            asChild
            className="my-1"
          >
            <Button
              variant="outline"
              role="combobox"
              disabled={disabled}
              aria-expanded={open}
              className="w-full justify-between h-8 p-2 text-gray-500 text-sm"
            >
              {field?.value ? items?.find((item: TDropdownWrapperItems) => item?.value === field.value)?.label : title}
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
        </Label>

        <PopoverContent
          className="w-[150px] p-0"
          align="start"
        >
          <Command>
            <CommandGroup>
              <CommandList>
                {items?.map((item: any) => (
                  <CommandItem
                    key={item.value}
                    disabled={disabled}
                    value={item?.value}
                    className={field?.value === item?.value ? "bg-gray-300" : undefined}
                    onSelect={() => {
                      field.onChange(item?.value);
                      setOpen(false);
                    }}
                    // defaultValue={defaultValue}
                  >
                    {item.label}
                  </CommandItem>
                ))}
              </CommandList>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      {error && <p className="text-red-500 text-xs ml-1">{error}</p>}
    </div>
  );
}
