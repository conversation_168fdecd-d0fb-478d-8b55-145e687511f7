"use client";

import { ChevronDown, ChevronUp } from "lucide-react";
import { useSearchParams } from "next/navigation";

type TSortableColumn = {
  heading: string;
  name: string;
  onSort: (name: string) => void;
};

export default function SortableColumn({ heading, name, onSort }: TSortableColumn) {
  const searchParams = useSearchParams();
  const sortOrder = searchParams.get("sortOrder");
  const sortBy = searchParams.get("sortBy");

  return (
    <div
      onClick={() => onSort(name)}
      className="flex items-center group gap-1"
    >
      <span>{heading}</span>
      {sortBy === name ? (
        <>{sortOrder === "ASC" ? <ChevronDown size="16px" /> : <ChevronUp size="16px" />} </>
      ) : (
        <button className="invisible group-hover:visible">
          {sortOrder === "ASC" ? <ChevronDown size="16px" /> : <ChevronUp size="16px" />}
        </button>
      )}
    </div>
  );
}
