import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import ComboBoxWrapper from "./ComboBoxWrapper";
import FilterDatePicker from "./FilterDatePicker";

const storeStatus = [
  {
    value: "ACTIVE",
    label: "Active",
  },
  {
    value: "INACTIVE",
    label: "Inactive",
  },
  {
    value: "UNINSTALLED",
    label: "Uninstalled",
  },
  {
    value: "CLOSED",
    label: "Closed",
  },
  {
    value: "DELETED",
    label: "Deleted",
  },
];

const plans = [
  {
    value: "ALL",
    label: "All",
  },
  {
    value: "FREE",
    label: "Free",
  },
  {
    value: "PRO",
    label: "Pro",
  },
];

type TActionButtons = {
  onClearQuery: () => void;
  onStatus: (status: string) => void;
};

export default function ActionButtons({ onClearQuery, onStatus }: TActionButtons) {
  const searchParams = useSearchParams();
  const planType = searchParams.get("plan");
  const status = searchParams.get("status");
  return (
    <div className="w-full flex flex-col items-start md:flex-row space-y-2 md:space-y-0 md:space-x-3 flex-shrink-0 px-2 pb-2">
      <div className="flex flex-wrap items-start gap-2 w-full relative">
        <ComboBoxWrapper
          title="Status"
          items={storeStatus}
          onSelect={onStatus}
          value={status as string}
        />

        <ComboBoxWrapper
          title="Plan"
          items={plans}
          //   onSelect={onPlan}
          value={planType as string}
        />

        <FilterDatePicker popoverAlign="start" />
        <Button
          onClick={onClearQuery}
          size="sm"
          variant="secondary"
        >
          Clear All
        </Button>
      </div>
    </div>
  );
}
