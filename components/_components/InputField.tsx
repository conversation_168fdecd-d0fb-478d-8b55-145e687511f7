import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React from "react";

interface InputFieldProps {
  label?: string;
  type?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  value?: string;
  error?: string;
  helpText?: string;
  readonly?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const InputField = React.forwardRef(function Component(
  { label, required = true, helpText, type = "text", readonly, error, value = "", ...restProps }: InputFieldProps,
  ref: any
) {
  return (
    <div className="flex flex-col gap-1">
      <Label className="text-[13px] text-gray-500 font-normal">
        {label}
        {required && <span className="text-red-500"> *</span>}
        <Input
          className="h-8 text-[13px] text-gray-800 text-sm w-full mt-1"
          ref={ref}
          type={type}
          value={value}
          {...restProps}
          readOnly={readonly}
        />
      </Label>
      {helpText && <span className="text-xs text-gray-500">{helpText}</span>}
      {error && <p className="text-red-500 text-xs ml-1">{error}</p>}
    </div>
  );
});

export default InputField;
