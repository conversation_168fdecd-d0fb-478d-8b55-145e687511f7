"use client";

import { useSidebar } from "@/modules/hooks/useSidebar";
import { MenuIcon } from "lucide-react";

export default function TopbarMenu() {
  const { setSidebarOpen } = useSidebar();

  return (
    <>
      <button
        type="button"
        className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
        onClick={() => setSidebarOpen(true)}
      >
        <span className="sr-only">Open sidebar</span>
        <MenuIcon
          className="h-6 w-6"
          aria-hidden="true"
        />
      </button>

      <div
        className="h-6 w-px bg-gray-900/10 lg:hidden"
        aria-hidden="true"
      />
    </>
  );
}
