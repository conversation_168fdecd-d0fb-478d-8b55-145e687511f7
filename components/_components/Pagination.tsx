import { Button } from "@/components/ui/button";
import usePagination, { DOTS } from "@/modules/hooks/usePagination";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { Dispatch, SetStateAction } from "react";

const Pagination = ({
  count,
  shopCount,
  siblingCount = 1,
  currentPage,
  setCurrentPage,
  limit,
}: {
  count: number;
  shopCount: number;
  siblingCount?: number;
  currentPage: number;
  setCurrentPage?: Dispatch<SetStateAction<number>>;
  limit: number;
}) => {
  const totalCount = Math.ceil(count / limit);

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
  });

  return (
    <div className="flex justify-between items-center py-2 px-4">
      <span className="font-medium text-sm text-gray-900 dark:text-white">
        Total {shopCount}/{count} Results
      </span>

      {/* {totalCount > 1 && ( */}
      <div className="flex items-center">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage && setCurrentPage((currentPage as number) - 1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0 rounded-r-none -mr-[1px]"
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>

        {paginationRange?.map((pageNumber, idx) => (
          <div key={idx}>
            {pageNumber === DOTS ? (
              <Button
                size="sm"
                variant="outline"
                className="rounded-none"
              >
                ...
              </Button>
            ) : (
              <Button
                key={idx}
                size="sm"
                variant="outline"
                disabled={pageNumber === currentPage}
                onClick={() => {
                  setCurrentPage && setCurrentPage(pageNumber as number);
                }}
                className={`${pageNumber === currentPage ? "bg-gray-200" : ""} rounded-none `}
              >
                {pageNumber}
              </Button>
            )}
          </div>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage && setCurrentPage(currentPage + 1)}
          disabled={currentPage === totalCount}
          className="h-8 w-8 p-0 rounded-l-none"
        >
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </div>
      {/* )} */}
    </div>
  );
};

export default Pagination;
