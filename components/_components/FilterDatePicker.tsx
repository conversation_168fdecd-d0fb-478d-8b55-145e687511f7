"use client";

import { DateRangePicker } from "@/components/ui/custom/date-range-picker";
import dayjs from "dayjs";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { DateRange } from "react-day-picker";

type TPopoverAlign = "start" | "center" | "end";

export default function FilterDatePicker({ popoverAlign }: { popoverAlign: TPopoverAlign }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const from = searchParams.get("startDate");
  const to = searchParams.get("endDate");
  const [filterDateRange, setFilterDateRange] = useState<DateRange | null>(null);

  const createQueryString = useCallback(
    (names: string | string[], values: string | string[]) => {
      const params = new URLSearchParams(searchParams.toString());
      if (Array.isArray(names) && Array.isArray(values)) {
        names.forEach((name, index) => {
          params.set(name, values[index]);
        });
      } else if (!Array.isArray(names) && !Array.isArray(values)) {
        params.set(names, values);
      }
      return params.toString();
    },
    [searchParams]
  );

  useEffect(() => {
    const searchQuery =
      filterDateRange &&
      "?" +
        createQueryString(
          ["startDate", "endDate"],
          [dayjs(filterDateRange.from).format("YYYY-MM-DD"), dayjs(filterDateRange.to).format("YYYY-MM-DD")]
        );
    searchQuery && router.push(pathname + searchQuery);
  }, [createQueryString, filterDateRange, pathname, router]);

  const getThisMonthDates = () => {
    const from = new Date();
    const to = new Date();
    from.setDate(1);
    from.setHours(0, 0, 0, 0);
    to.setHours(23, 59, 59, 999);
    return { from, to };
  };

  return (
    <DateRangePicker
      onUpdate={(values) => setFilterDateRange(values?.range as DateRange)}
      align={popoverAlign}
      locale="en-GB"
      showCompare={false}
      initialDateFrom={from || getThisMonthDates().from}
      initialDateTo={to || getThisMonthDates().to}
    />
  );
}
