"use client";

import { Search } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";

type TSearchBar = {
  placeholder: string;
  setCurrentPage?: Dispatch<SetStateAction<number>>;
};

export default function SearchBar({ placeholder, setCurrentPage = () => {} }: TSearchBar) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();

  const search = searchParams.get("search");
  const [searchTerm, setSearchTerm] = useState(search || "");

  const debouncedSearchParams = useDebouncedCallback(({ searchTerm }: { searchTerm: string }) => {
    const currentSearchParams = new URLSearchParams(searchParams.toString());

    if (searchTerm) {
      currentSearchParams.set("search", searchTerm);
    } else {
      currentSearchParams.delete("search");
    }
    router.push(`${pathname}?${currentSearchParams.toString()}`);
  }, 500);

  const handleSearch = (e: { target: { value: any } }) => {
    const term = e.target.value;
    setSearchTerm(term);
    setCurrentPage(1);
  };

  useEffect(() => {
    debouncedSearchParams({ searchTerm });
  }, [debouncedSearchParams, searchTerm]);

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
      }}
      className="flex-1"
    >
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search
            size="16px"
            className="text-gray-600"
          />
        </div>
        <input
          type="text"
          id="simple-search"
          autoFocus
          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-md block pl-10 h-8 w-full"
          placeholder={placeholder}
          onChange={handleSearch}
          value={searchTerm}
        />
      </div>
    </form>
  );
}
