import { JSX } from "react";

export type TDescriptionListData = {
  label?: string;
  content: JSX.Element;
}[];

export default function DescriptionList({ listData }: { listData: TDescriptionListData }) {
  return (
    <>
      {listData?.map((item, idx) => (
        <div
          key={idx}
          className="py-3 flex gap-2 border-b last:border-b-0"
        >
          {item?.label && (
            <p className="text-sm font-medium leading-6 text-gray-900 basis-[150px] min-w-[150px]">{item?.label}</p>
          )}

          <div className="flex-1 break-all">{item.content}</div>
        </div>
      ))}
    </>
  );
}
