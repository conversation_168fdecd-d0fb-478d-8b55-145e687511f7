"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { loginAction } from "@/modules/_actions/LoginAction";
import { LoginActionSchema } from "@/modules/validations/login";
import { LoginFormValues } from "@/types/common";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import InputField from "./InputField";

export default function LoginForm() {
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  const callbackUrl =
    searchParams.get("callbackUrl") && !(searchParams?.get("callbackUrl") as string).includes("login")
      ? searchParams.get("callbackUrl")
      : "/dashboard";

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(LoginActionSchema),
  });

  const onSubmit = async (data: LoginFormValues) => {
    setLoading(true);
    try {
      const response = await loginAction(data);

      if (response?.success) {
        toast.success(response.message);
        router.replace(callbackUrl as string);
      } else {
        setFormError(response?.message as string);
      }
    } catch (error) {
      setFormError("An error occurred while logging in");
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 5000);
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-6 sm:max-w-sm w-full"
    >
      {formError && <p className="text-red-500 text-sm text-center">{formError}</p>}
      {/* Email */}
      <Controller
        name="email"
        control={control}
        render={({ field }) => (
          <InputField
            label="Email"
            type="email"
            placeholder="Enter Your Email"
            error={errors?.email?.message}
            {...field}
          />
        )}
      />

      {/* Password */}
      <Controller
        name="password"
        control={control}
        render={({ field }) => (
          <InputField
            label="Password"
            type="password"
            placeholder="Enter Your Password"
            error={errors?.password?.message}
            {...field}
          />
        )}
      />

      <div className="flex items-center space-x-2">
        <Checkbox id="terms" />
        <label
          htmlFor="terms"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          Remember Me
        </label>
      </div>

      <Button
        className="w-full"
        size="sm"
        type="submit"
        disabled={loading}
      >
        {loading ? "Signing in..." : "Sign In"}
      </Button>
    </form>
  );
}
