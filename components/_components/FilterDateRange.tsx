import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useState } from "react";
import { Label } from "../ui/label";

export default function FilterDateRange({
  value,
  onChange,
  label,
  required = true,
  error = "",
}: {
  value: Date;
  onChange: (date: Date) => void;
  label?: string;
  required?: boolean;
  error?: string;
}) {
  const [date] = useState(null);

  return (
    <div>
      <Popover>
        <Label className="text-[13px] text-gray-500 font-normal mb-1 inline-block">
          {label}
          {required && <span className="text-red-500"> *</span>}
        </Label>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn("w-full h-8 justify-start text-left font-normal", !date && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? format(value, "PP") : <span>dd/mm/yyyy</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2">
          <Calendar
            mode="single"
            selected={value}
            onSelect={(date) => date && onChange(date)}
            initialFocus
          />
        </PopoverContent>
      </Popover>
      {error && <p className="text-red-500 text-xs ml-1">{error}</p>}
    </div>
  );
}
