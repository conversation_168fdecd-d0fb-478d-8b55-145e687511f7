import { Prisma } from "@prisma/client";
import dayjs from "dayjs";
import { ZodError } from "zod";

export default function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export function exclude<T extends Record<string, unknown>, Key extends keyof T>(
  data: T | null,
  keys: Key[]
): Omit<T | null, Key> | null {
  if (!data) return null;
  return Object.fromEntries(Object.entries(data).filter(([key]) => !keys.includes(key as Key))) as Omit<T, Key>;
}

export const convertZodErrors = (error: ZodError) => {
  return error.issues.reduce((acc: { [key: string]: string }, issue) => {
    acc[issue.path[0]] = issue.message;
    return acc;
  }, {});
};

export const getFirstZodError = (error: ZodError): string => {
  return error.issues[0]?.message || "An unknown error occurred";
};

export const handlePrismaError = (err: any, message: string = "") => {
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    switch (err.code) {
      case "P2002":
        return { success: false, message: message || "Unique constraint failed" };
      default:
        return { success: false, message: "Database error occurred" };
    }
  }
  return { success: false, message: "An unexpected database error occurred" };
};

export const convertToIng = (word: string) => {
  if (/e$/.test(word)) {
    // If the word ends with "e", drop the "e" and add "ing"
    return word.slice(0, -1) + "ing";
  } else if (/[aeiou][^aeiou]$/.test(word)) {
    // For words ending in a consonant-vowel-consonant pattern, double the last consonant and add "ing"
    return word + word.slice(-1) + "ing";
  } else {
    // For all other cases, simply add "ing"
    return word + "ing";
  }
};

export const formatCustomDate = (inputDate: string, time: number = 0) => {
  const date = new Date(inputDate);
  if (time === 23) {
    // For end of day, set to 23:59:59.999
    date.setHours(23, 59, 59, 999);
  } else {
    // For start of day or specific hour, set to exact hour with 0 minutes/seconds
    date.setHours(time, 0, 0, 0);
  }
  return date.toISOString();
};

export const formattedDate = (date: string) => {
  if (date) return dayjs(date).format("DD MMM, YYYY");
  return null;
};

export const formattedDateTime = (date: string) => {
  return dayjs(date).format("hh:mm A, DD MMM YYYY");
};

export const calculateDynamicId = (index: number, currentPage: number, limit: number) => {
  return (currentPage - 1) * limit + index + 1;
};

export const serializePrismaResponse = (data: Record<string, unknown> | Record<string, unknown>[] | null) => {
  return JSON.parse(JSON.stringify(data, (key, value) => (typeof value === "bigint" ? Number(value) : value)));
};

export const generateSlug = (name: string) => {
  return name
    .replace(/[^a-zA-Z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "")
    .toLowerCase();
};
