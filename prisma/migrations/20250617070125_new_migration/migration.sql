-- CreateTable
CREATE TABLE "admin" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR NOT NULL,
    "email" VARCHAR NOT NULL,
    "password" VARCHAR NOT NULL,
    "type" INTEGER NOT NULL,
    "scopes" J<PERSON>NB NOT NULL DEFAULT '"[]"',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "admin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "code" VARCHAR(255) NOT NULL,
    "discount_type" VARCHAR(100) NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "discount_limit_duration" INTEGER,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "max_limit" INTEGER NOT NULL DEFAULT 9999,
    "redeem_count" INTEGER NOT NULL DEFAULT 0,
    "plans" JSONB NOT NULL DEFAULT '[]',
    "status" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "coupon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscription_plans" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "slug" VARCHAR(255) NOT NULL,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "type" VARCHAR(100) NOT NULL,
    "interval" VARCHAR(100) NOT NULL,
    "package_info" JSONB NOT NULL DEFAULT '[]',
    "meta" JSONB NOT NULL DEFAULT '{}',
    "trial_days" INTEGER NOT NULL DEFAULT 0,
    "currency" VARCHAR(100) NOT NULL DEFAULT 'USD',
    "coupon_id" BIGINT,
    "test" BOOLEAN NOT NULL DEFAULT false,
    "price" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "discount_type" VARCHAR(100),
    "discount" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "final_price" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscription_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscription_transactions" (
    "id" BIGSERIAL NOT NULL,
    "shop_domain" VARCHAR(255) NOT NULL,
    "plan_slug" VARCHAR(255) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "slug" VARCHAR(255) NOT NULL,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "type" VARCHAR(100) NOT NULL DEFAULT 'free',
    "interval" VARCHAR(100) NOT NULL DEFAULT 'monthly',
    "transaction_type" VARCHAR(255) NOT NULL DEFAULT 'new',
    "transaction_narration" VARCHAR(255) NOT NULL DEFAULT 'new subscription',
    "plan_data" JSONB NOT NULL,
    "requested_data" JSONB NOT NULL,
    "subscription_expire_date" TIMESTAMP(3) NOT NULL,
    "is_paid" BOOLEAN NOT NULL DEFAULT false,
    "trial_days" INTEGER NOT NULL DEFAULT 0,
    "trial_ends_on" TIMESTAMP(3),
    "currency" VARCHAR(100) NOT NULL DEFAULT 'USD',
    "test" BOOLEAN NOT NULL DEFAULT false,
    "price" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "discount_type" VARCHAR(100),
    "discount" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "final_price" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "coupon_code" VARCHAR(255),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "meta" JSONB,

    CONSTRAINT "subscription_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupons_applied" (
    "id" BIGSERIAL NOT NULL,
    "shop_domain" VARCHAR(255) NOT NULL,
    "app_subscription_id" VARCHAR(255),
    "coupon_id" BIGINT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "code" VARCHAR(255) NOT NULL,
    "discount_type" VARCHAR(100) NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "coupons_applied_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shop_webhooks" (
    "id" BIGSERIAL NOT NULL,
    "shop_domain" VARCHAR(255) NOT NULL,
    "wh_subs_id" TEXT NOT NULL,
    "topic" VARCHAR(100) NOT NULL,
    "delivery_method" VARCHAR(50) NOT NULL,
    "response" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shop_webhooks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscription_usage" (
    "id" BIGSERIAL NOT NULL,
    "shop_domain" VARCHAR(255) NOT NULL,
    "plan_slug" VARCHAR(255) NOT NULL,
    "subscription_id" VARCHAR(255) NOT NULL,
    "subscription_line_id" VARCHAR(255) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "key" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "price" DOUBLE PRECISION NOT NULL DEFAULT 0.00,
    "meta" JSONB NOT NULL,
    "occurrence" INTEGER NOT NULL DEFAULT 1,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "subscription_usage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "admin_email_key" ON "admin"("email");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_code_key" ON "coupon"("code");

-- CreateIndex
CREATE UNIQUE INDEX "subscription_plans_slug_key" ON "subscription_plans"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "coupons_applied_code_key" ON "coupons_applied"("code");

-- CreateIndex
CREATE UNIQUE INDEX "shop_webhooks_wh_subs_id_key" ON "shop_webhooks"("wh_subs_id");

-- AddForeignKey
ALTER TABLE "subscription_plans" ADD CONSTRAINT "subscription_plans_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscription_transactions" ADD CONSTRAINT "subscription_transactions_plan_slug_fkey" FOREIGN KEY ("plan_slug") REFERENCES "subscription_plans"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;
