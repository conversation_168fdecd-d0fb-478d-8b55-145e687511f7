const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

export default async function main() {
  console.log("Seeding data...");

  const admins = [
    {
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "$2a$10$8q4AO4EGiZAxc.j4gXaZvO5HXl3k9DADpGTsjYKD/tSsZDE4WPuQi",
      type: 1,
      scopes: '["DASHBOARD_READ"]',
    },
    {
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "$2a$10$8q4AO4EGiZAxc.j4gXaZvO5HXl3k9DADpGTsjYKD/tSsZDE4WPuQi",
      type: 1,
      scopes: '["DASHBOARD_READ"]',
    },
    {
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "$2a$10$8q4AO4EGiZAxc.j4gXaZvO5HXl3k9DADpGTsjYKD/tSsZDE4WPuQi",
      type: 1,
      scopes: '["DASHBOARD_READ"]',
    },
  ];

  // Upsert (create or update) admins
  for (const admin of admins) {
    await prisma.admin.upsert({
      where: { email: admin.email },
      update: {
        name: admin.name,
        password: admin.password,
        type: admin.type,
        scopes: admin.scopes,
      },
      create: admin,
    });
  }

  console.log("Seeding completed!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
