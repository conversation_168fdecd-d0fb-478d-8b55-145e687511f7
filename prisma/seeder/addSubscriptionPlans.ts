const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

const discountTypes = {
  PERCENT: "percent",
  AMOUNT: "amount",
};

const planIntervals = {
  MONTHLY: "monthly",
  YEARLY: "yearly",
  LIFETIME: "lifetime",
  VISIONARY: "visionary",
  USAGE: "usage",
};

const planTypes = {
  FREE: "free",
  PRO: "pro",
};

const getPlanPackageInfo = [
  {
    slug: `pro-${planIntervals.MONTHLY}`,
    benefits: [
      {
        name: "all_option_types_without_file_upload",
        display_name: "All option types (except File Upload)",
        value: false,
      },
      {
        display_name: "Unlimited variants options",
        value: true,
        name: "unlimited_variants_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
      },
      {
        display_name: "File Upload",
        value: true,
        name: "file_upload",
      },
      {
        display_name: "Price Add-ons, multi-currency support",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Conditional logic – Shopify Variants",
        value: true,
        name: "conditional_logic_shopify_variants",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },
  {
    slug: `pro-${planIntervals.YEARLY}`,
    benefits: [
      {
        display_name: "All option types (except File Upload)",
        value: true,
        name: "all_option_types_without_file_upload",
      },
      {
        display_name: "Unlimited variants options",
        value: true,
        name: "unlimited_variants_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
        limit: 0,
      },
      {
        display_name: "File Upload",
        value: true,
        name: "file_upload",
        limit: 0,
      },
      {
        display_name: "Price Add-ons, multi-currency support",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Conditional logic – Shopify Variants",
        value: true,
        name: "conditional_logic_shopify_variants",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },
  {
    slug: `visionary-${planIntervals.USAGE}`,
    benefits: [
      {
        display_name: "All option types (except File Upload)",
        value: true,
        name: "all_option_types_without_file_upload",
      },
      {
        display_name: "Unlimited variants options",
        value: true,
        name: "unlimited_variants_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
      },
      {
        display_name: "File Upload",
        value: true,
        name: "file_upload",
      },
      {
        display_name: "Price Add-ons, multi-currency support",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Conditional logic – Shopify Variants",
        value: true,
        name: "conditional_logic_shopify_variants",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },
  {
    slug: `free-${planIntervals.LIFETIME}`,
    benefits: [
      {
        display_name: "All option types (except File Upload)",
        value: true,
        name: "all_option_types_without_file_upload",
      },
      {
        display_name: "Unlimited variants options",
        value: true,
        name: "unlimited_variants_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
    ],
  },
];

const planMetaData = [
  {
    slug: `free-${planIntervals.LIFETIME}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `pro-${planIntervals.MONTHLY}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `pro-${planIntervals.YEARLY}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "save 20% on yearly", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `visionary-${planIntervals.USAGE}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "save 50% on visionary", // individual plan save description if any
      capped_amount: { amount: 20.0, currencyCode: "USD" },
      terms: "",
      duration: 18, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
];

const plans = [
  {
    name: "Free",
    slug: `free-${planIntervals.LIFETIME}`,
    status: true,
    type: planTypes.FREE,
    interval: planIntervals.LIFETIME,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `free-${planIntervals.LIFETIME}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `free-${planIntervals.LIFETIME}`)?.meta,
    trial_days: 0,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 0.0,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 0.0,
  },
  {
    name: "Pro",
    slug: `pro-${planIntervals.MONTHLY}`,
    status: true,
    type: planTypes.PRO,
    interval: planIntervals.MONTHLY,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `pro-${planIntervals.MONTHLY}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `pro-${planIntervals.MONTHLY}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 9.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 9.99,
  },
  {
    name: "Pro",
    slug: `pro-${planIntervals.YEARLY}`,
    status: true,
    type: planTypes.PRO,
    interval: planIntervals.YEARLY,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `pro-${planIntervals.YEARLY}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `pro-${planIntervals.YEARLY}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 99.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 99.99,
  },
  {
    name: "Visionary",
    slug: `visionary-${planIntervals.USAGE}`,
    status: true,
    type: planTypes.PRO,
    interval: planIntervals.USAGE,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `visionary-${planIntervals.USAGE}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `visionary-${planIntervals.USAGE}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 199.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 199.99,
  },
];

const seed = async () => {
  try {
    plans.map(async (plan) => {
      await prisma.subscription_plans.create({
        data: {
          ...plan,
        },
      });
    });

    // Replace console.log with a logging utility if available
    console.log("All plans have been seeded successfully.");
  } catch (error) {
    console.error("Error seeding plans:", error);
  }
};

export default seed;
