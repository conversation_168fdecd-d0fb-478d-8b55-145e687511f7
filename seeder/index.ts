{
  require("dotenv").config({ path: "../.env" });
  const { Datastore } = require("@google-cloud/datastore");

  // Creates a client
  const datastore = new Datastore({
    namespace: process.env.DATASTORE_NAMESPACE,
    projectId: process.env.DATASTORE_PROJECTID,
  });

  const dummyShops = [
    {
      billingAddress: {
        phone: "1234567890",
        formattedArea: "TORONTO ON, Canada",
        formatted: ["123 Main Street", "TORONTO ON M1A 2B3", "Canada"],
        zip: "M1A 2B3",
        provinceCode: "ON",
        province: "Ontario",
        longitude: -79.3832,
        latitude: 43.6532,
        id: "gid://shopify/ShopAddress/59102625858",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Tech Co.",
        city: "TORONTO",
        address2: "Suite 200",
        address1: "123 Main Street",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "abcd5678-1234-90ef-ghij-2345678901mn",
      currencyCode: "USD",
      currencyFormats: {
        moneyWithCurrencyFormat: "${{amount_with_comma_separator}} USD",
      },
      domain: "randomstore1.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500318",
      isAddonsFree: false,
      isExtensionEnabled: true,
      isPartnerDevelopment: false,
      isSubscriptionActive: true,
      name: "Random Store 1",
      phone: "9876543210",
      shopifyPlan: {
        shopifyPlus: "true",
        partnerDevelopment: "false",
        displayName: "Basic",
      },
      url: "https://www.randomstore1.com",
      createdAt: "2024-12-09",
      status: "ACTIVE",
    },
    {
      billingAddress: {
        phone: "2506888132",
        formattedArea: "VANCOUVER BC, Canada",
        formatted: ["789 Oak Avenue", "VANCOUVER BC V5K 1C5", "Canada"],
        zip: "V5K 1C5",
        provinceCode: "BC",
        province: "British Columbia",
        longitude: -123.1216,
        latitude: 49.2827,
        id: "gid://shopify/ShopAddress/59102625859",
        countryCodeV2: "CA",
        country: "Canada",
        company: null,
        city: "VANCOUVER",
        address2: "",
        address1: "789 Oak Avenue",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "efgh5678-1234-90ef-ghij-3456789012op",
      currencyCode: "CAD",
      currencyFormats: {
        moneyWithCurrencyFormat: "C${{amount_with_comma_separator}} CAD",
      },
      domain: "randomstore2.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500319",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 2",
      phone: "0498123456",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore2.com",
      createdAt: "2024-12-10",
      status: "INACTIVE",
    },
    {
      billingAddress: {
        phone: "4501234567",
        formattedArea: "MONTREAL QC, Canada",
        formatted: ["456 Maple Drive", "MONTREAL QC H2Y 3B7", "Canada"],
        zip: "H2Y 3B7",
        provinceCode: "QC",
        province: "Quebec",
        longitude: -73.5673,
        latitude: 45.5017,
        id: "gid://shopify/ShopAddress/59102625860",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Maple Solutions",
        city: "MONTREAL",
        address2: "Building B",
        address1: "456 Maple Drive",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "ijkl5678-1234-90ef-ghij-4567890123qr",
      currencyCode: "EUR",
      currencyFormats: {
        moneyWithCurrencyFormat: "€{{amount_with_comma_separator}} EUR",
      },
      domain: "randomstore3.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500320",
      isAddonsFree: false,
      isExtensionEnabled: true,
      isPartnerDevelopment: false,
      isSubscriptionActive: true,
      name: "Random Store 3",
      phone: "4509876543",
      shopifyPlan: {
        shopifyPlus: "true",
        partnerDevelopment: "false",
        displayName: "Shopify Plus",
      },
      url: "https://www.randomstore3.com",
      createdAt: "2024-12-11",
      status: "ACTIVE",
    },

    {
      billingAddress: {
        phone: "9025551234",
        formattedArea: "HALIFAX NS, Canada",
        formatted: ["789 Water Street", "HALIFAX NS B3H 1Y3", "Canada"],
        zip: "B3H 1Y3",
        provinceCode: "NS",
        province: "Nova Scotia",
        longitude: -63.5752,
        latitude: 44.6488,
        id: "gid://shopify/ShopAddress/59102625861",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Atlantic Ventures",
        city: "HALIFAX",
        address2: null,
        address1: "789 Water Street",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "randomstore4.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 4",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore4.com",
      createdAt: "2024-12-10",
      status: "INACTIVE",
    },

    {
      billingAddress: {
        phone: "1234567890",
        formattedArea: "TORONTO ON, Canada",
        formatted: ["123 Main Street", "TORONTO ON M1A 2B3", "Canada"],
        zip: "M1A 2B3",
        provinceCode: "ON",
        province: "Ontario",
        longitude: -79.3832,
        latitude: 43.6532,
        id: "gid://shopify/ShopAddress/59102625858",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Tech Co.",
        city: "TORONTO",
        address2: "Suite 200",
        address1: "123 Main Street",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "abcd5678-1234-90ef-ghij-2345678901mn",
      currencyCode: "USD",
      currencyFormats: {
        moneyWithCurrencyFormat: "${{amount_with_comma_separator}} USD",
      },
      domain: "randomstore5.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500318",
      isAddonsFree: false,
      isExtensionEnabled: true,
      isPartnerDevelopment: false,
      isSubscriptionActive: true,
      name: "Random Store 5",
      phone: "9876543210",
      shopifyPlan: {
        shopifyPlus: "true",
        partnerDevelopment: "false",
        displayName: "Basic",
      },
      url: "https://www.randomstore5.com",
      createdAt: "2024-02-09",
      status: "ACTIVE",
    },

    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "efgh5678-1234-90ef-ghij-3456789012op",
      currencyCode: "CAD",
      currencyFormats: {
        moneyWithCurrencyFormat: "C${{amount_with_comma_separator}} CAD",
      },
      domain: "randomstore6.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500319",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 6",
      phone: "0498123456",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore6.com",
      createdAt: "2024-05-10",
      status: "INACTIVE",
    },

    {
      billingAddress: {
        phone: "4501234567",
        formattedArea: "MONTREAL QC, Canada",
        formatted: ["456 Maple Drive", "MONTREAL QC H2Y 3B7", "Canada"],
        zip: "H2Y 3B7",
        provinceCode: "QC",
        province: "Quebec",
        longitude: -73.5673,
        latitude: 45.5017,
        id: "gid://shopify/ShopAddress/59102625860",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Maple Solutions",
        city: "MONTREAL",
        address2: "Building B",
        address1: "456 Maple Drive",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "ijkl5678-1234-90ef-ghij-4567890123qr",
      currencyCode: "EUR",
      currencyFormats: {
        moneyWithCurrencyFormat: "€{{amount_with_comma_separator}} EUR",
      },
      domain: "randomstore7.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500320",
      isAddonsFree: false,
      isExtensionEnabled: true,
      isPartnerDevelopment: false,
      isSubscriptionActive: true,
      name: "Random Store 7",
      phone: "4509876543",
      shopifyPlan: {
        shopifyPlus: "true",
        partnerDevelopment: "false",
        displayName: "Shopify Plus",
      },
      url: "https://www.randomstore7.com",
      createdAt: "2024-10-11",
      status: "ACTIVE",
    },

    {
      billingAddress: {
        phone: "9025551234",
        formattedArea: "HALIFAX NS, Canada",
        formatted: ["789 Water Street", "HALIFAX NS B3H 1Y3", "Canada"],
        zip: "B3H 1Y3",
        provinceCode: "NS",
        province: "Nova Scotia",
        longitude: -63.5752,
        latitude: 44.6488,
        id: "gid://shopify/ShopAddress/59102625861",
        countryCodeV2: "CA",
        country: "Canada",
        company: "Atlantic Ventures",
        city: "HALIFAX",
        address2: null,
        address1: "789 Water Street",
      },
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "randomstore8.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 8",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore8.com",
      createdAt: "2024-09-10",
      status: "INACTIVE",
    },

    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "randomstore9.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 9",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore9.com",
      createdAt: "2024-02-09",
      status: "INACTIVE",
    },

    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "randomstore10.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 10",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore10.com",
      createdAt: "2024-12-11",
      status: "ACTIVE",
    },

    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "randomstore11.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "Random Store 11",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.randomstore11.com",
      createdAt: "2024-12-18",
      status: "ACTIVE",
    },

    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "pial1.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "pial 1",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.pial1.com",
      createdAt: "2024-12-18",
      status: "ACTIVE",
    },
    {
      billingAddress: {},
      billingConfigKey: "Recurring billing plan",
      crispSessionId: "mnop5678-1234-90ef-ghij-5678901234st",
      currencyCode: "GBP",
      currencyFormats: {
        moneyWithCurrencyFormat: "£{{amount_with_comma_separator}} GBP",
      },
      domain: "pial10.myshopify.com",
      email: "<EMAIL>",
      globalId: "gid://shopify/Shop/81564500321",
      isAddonsFree: true,
      isExtensionEnabled: false,
      isPartnerDevelopment: true,
      isSubscriptionActive: false,
      name: "pial 10",
      phone: "9021112233",
      shopifyPlan: {
        shopifyPlus: "false",
        partnerDevelopment: "true",
        displayName: "Advanced",
      },
      url: "https://www.pial10.com",
      createdAt: "2024-12-18",
      status: "ACTIVE",
    },
  ];

  const createShops = async () => {
    const transaction = datastore.transaction();
    try {
      await transaction.run();
      dummyShops.forEach((shop) => {
        const key = datastore.key(["Shop", shop.domain]);
        const shopEntity = {
          key,
          data: {
            ...shop,
            createdAt: new Date(shop.createdAt).toISOString(),
          },
        };
        transaction.save(shopEntity);
      });
      await transaction.commit();
      console.log("Shops inserted successfully.");
    } catch (error) {
      console.error("Error inserting shops:", error);
      await transaction.rollback();
    }
  };

  createShops();
}
