import { IAddSubscriptionPlan } from "@/types/plans";
import { serializePrismaResponse } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";
import { prisma } from "./db";

export async function getPlans(search?: string, sortBy: string = "id", sortOrder: string = "ASC") {
  const whereClause: Prisma.subscription_plansWhereInput = search
    ? {
        OR: [{ name: { contains: search, mode: "insensitive" } }, { slug: { contains: search, mode: "insensitive" } }],
      }
    : {};

  const response = await prisma.subscription_plans.findMany({
    where: whereClause,
    orderBy: { [sortBy]: sortOrder.toLocaleLowerCase() },
  });

  const plans = serializePrismaResponse(response);

  return plans;
}

export async function getPlan(id: number) {
  const response = await prisma.subscription_plans.findUnique({ where: { id: id } });
  const plan = serializePrismaResponse(response);
  return plan;
}

export async function deletePlan(id: number) {
  return await prisma.subscription_plans.delete({ where: { id: id } });
}

export async function addPlan(plan: IAddSubscriptionPlan) {
  return await prisma.subscription_plans.create({ data: plan });
}

export async function updatePlan(id: number, data: IAddSubscriptionPlan) {
  return await prisma.subscription_plans.update({
    where: { id: id },
    data: data,
  });
}
