import datastore from "@/lib/datastore/index";
import { AggregateField, PropertyFilter } from "@google-cloud/datastore";

const Kind = "Option set";
const shopKind = "Shop";
const optionKind = "Option";

export const getOptionSet = async (
  search: string = "",
  sortBy: string = "createdAt",
  sortOrder: string = "DESC",
  page: string | number = 1,
  limit: string | number = 20,
  shop: string | null = null
) => {
  const pageLimit = Number(limit);
  const pageSize = Number(page);
  const offset = (pageSize - 1) * pageLimit;

  let query;

  if (shop) {
    const ancestorKey = datastore.client.key(["Shop", shop]);
    query = datastore.client
      .createQuery(Kind)
      .hasAncestor(ancestorKey)
      .order(sortBy, { descending: sortOrder === "ASC" })
      .limit(pageLimit)
      .offset(offset);
  } else {
    query = datastore.client
      .createQuery(Kind)
      .order(sortBy, { descending: sortOrder === "ASC" })
      .limit(pageLimit)
      .offset(offset);
  }

  // Apply search filters
  if (search) {
    query = query.filter(new PropertyFilter("title", "=", search));
  }

  const totalOptionsAggeregate = datastore.client
    .createAggregationQuery(datastore.client.createQuery(Kind))
    .addAggregation(AggregateField.count());

  const [[totalOptions]] = await datastore.client.runAggregationQuery(totalOptionsAggeregate);

  // Run the query
  const [options] = await datastore.client.runQuery(query);

  // Add keyData.id and keyData.parent.name to each entity
  const updatedOptions = options.map((entity) => {
    const symbols = Object.getOwnPropertySymbols(entity);
    const keySymbol = symbols.find((sym) => sym.toString() === "Symbol(KEY)");

    if (keySymbol) {
      const keyData = entity[keySymbol];
      return {
        ...entity,
        id: keyData.id,
        domain: keyData.parent?.name,
      };
    }

    return entity;
  });

  return {
    optionSet: updatedOptions,
    optionSetCount: updatedOptions.length,
    totalCount: totalOptions.property_1,
  };
};

export const getOptionDetails = async (domain: string, id: string) => {
  const optionSetKey = datastore.client.key([shopKind, domain, Kind, Number(id)]);
  const [optionSet] = await datastore.client.get(optionSetKey);

  const { optionIds } = optionSet;
  const optionKeys = optionIds?.map((id: string) => datastore.client.key([shopKind, domain, optionKind, id]));

  const [options] = await datastore.client.get(optionKeys);
  return options;
};

export const getOptionRules = async (domain: string, id: string) => {
  const optionRuleKey = datastore.client.key([shopKind, domain, Kind, Number(id)]);
  const [result] = await datastore.client.get(optionRuleKey);
  return result?.rules;
};

export const getOptionSetStatsByDomain = async (domain: string) => {
  try {
    const ancestorKey = datastore.client.key(["Shop", domain]);

    // Get all option sets for this domain
    const optionSetQuery = datastore.client
      .createQuery(Kind)
      .hasAncestor(ancestorKey);

    const [optionSets] = await datastore.client.runQuery(optionSetQuery);

    // Simply return the total count of option sets
    return {
      total: optionSets?.length || 0
    };
  } catch (error) {
    console.error('Error getting option set stats:', error);
    return {
      total: 0
    };
  }
};

export const getOptionStatsByDomain = async (domain: string) => {
  try {
    const ancestorKey = datastore.client.key(["Shop", domain]);

    // Query ALL options directly for this domain (not just those in option sets)
    const allOptionsQuery = datastore.client
      .createQuery(optionKind)
      .hasAncestor(ancestorKey);

    const [allOptions] = await datastore.client.runQuery(allOptionsQuery);



    if (!allOptions || allOptions.length === 0) {
      return {
        total: 0,
        withPrice: 0,
        withoutPrice: 0
      };
    }

    // Filter out null/undefined options and count valid ones
    const validOptions = allOptions.filter((option: any) => option !== null && option !== undefined);

    // Count options with and without prices
    let withPriceCount = 0;
    let withoutPriceCount = 0;

    validOptions.forEach((option: any) => {
      if (option.values && Array.isArray(option.values) && option.values.length > 0) {
        const hasPrice = option.values.some((value: any) =>
          value.price &&
          value.price !== "0" &&
          value.price !== "" &&
          value.price !== null &&
          value.price !== undefined &&
          parseFloat(value.price) > 0
        );

        if (hasPrice) {
          withPriceCount++;
        } else {
          withoutPriceCount++;
        }
      } else {
        withoutPriceCount++;
      }
    });



    return {
      total: validOptions.length,
      withPrice: withPriceCount,
      withoutPrice: withoutPriceCount
    };
  } catch (error) {
    console.error('Error getting option stats:', error);
    return {
      total: 0,
      withPrice: 0,
      withoutPrice: 0
    };
  }
};
