import datastoreClient from "./client";
import { getOptionDetails, getOptionRules, getOptionSet, getOptionSetStatsByDomain, getOptionStatsByDomain } from "./option";
import { getAllShopNames, getShopDetails, getShops, getShopsCount, getShopsDataByDate } from "./shop";

const datastore = {
  client: datastoreClient,
  shop: {
    ...{
      getShops,
      getAllShopNames,
      getShopsCount,
      getShopsDataByDate,
      getShopDetails,
    },
  },
  option: {
    ...{ getOptionSet, getOptionDetails, getOptionRules, getOptionSetStatsByDomain, getOptionStatsByDomain },
  },
};
export default datastore;
