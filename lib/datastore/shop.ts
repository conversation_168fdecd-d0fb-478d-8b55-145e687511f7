import datastore from "@/lib/datastore/index";
import { AggregateField, and, or, PropertyFilter } from "@google-cloud/datastore";
import { get } from "lodash";

const Kind = "Shop";

// Helper function to apply common filters to a query
function applyCommonFilters(query: any, search: string, status: string, startDate: string, endDate: string) {
  if (search) {
    query = query.filter(or([new PropertyFilter("email", "=", search), new PropertyFilter("domain", "=", search)]));
  }

  if (status) {
    query = query.filter(new PropertyFilter("status", "=", status));
  }

  if (startDate && endDate) {
    query = query.filter(
      and([
        new PropertyFilter("createdAt", ">=", new Date(startDate).toISOString()),
        new PropertyFilter("createdAt", "<=", new Date(endDate).toISOString()),
      ])
    );
  }

  return query;
}

export async function getShops(
  search: string = "",
  status: string = "",
  startDate: string = "",
  endDate: string = "",
  sortBy: string = "createdAt",
  sortOrder: string = "DESC",
  page: number | string = 1,
  limit: number | string = 1,
  plan: string = ""
) {
  const pageLimit = Number(limit);
  const pageSize = Number(page);
  const offset = (pageSize - 1) * pageLimit;

  // For plan filtering, we need to handle pagination differently
  if (plan && plan !== "ALL") {
    // Get all matching shops first (without pagination)
    let allShopsQuery = datastore.client
      .createQuery(Kind)
      .order(sortBy, { descending: sortOrder === "DESC" });

    // Apply common filters
    allShopsQuery = applyCommonFilters(allShopsQuery, search, status, startDate, endDate);

    const [allShops] = await datastore.client.runQuery(allShopsQuery);

    // Filter by plan
    let filteredShops = allShops;
    if (plan === "FREE") {
      filteredShops = allShops.filter((shop: any) => shop.isSubscriptionActive === false);
    } else if (plan === "PRO") {
      filteredShops = allShops.filter((shop: any) => shop.isSubscriptionActive === true);
    }

    // Apply pagination to filtered results
    const paginatedShops = filteredShops.slice(offset, offset + pageLimit);

    return {
      stores: paginatedShops,
      shopsCount: paginatedShops.length,
      totalCount: filteredShops.length,
    };
  }

  // Original logic for non-plan filtering
  let query = datastore.client
    .createQuery(Kind)
    .order(sortBy, { descending: sortOrder === "DESC" })
    .limit(pageLimit)
    .offset(offset);

  // Apply common filters
  query = applyCommonFilters(query, search, status, startDate, endDate);

  const totalStoresAggeregate = datastore.client
    .createAggregationQuery(datastore.client.createQuery(Kind))
    .addAggregation(AggregateField.count());

  const [[totalStores]] = await datastore.client.runAggregationQuery(totalStoresAggeregate);

  // Run the query
  const [shops] = await datastore.client.runQuery(query);

  return {
    stores: shops,
    shopsCount: shops.length,
    totalCount: totalStores.property_1,
  };
}

// export async function createShop() {
//   const shop = await datastore.client.update({
//     entity: {
//       key: datastore.client.key([Kind, "pial-live.myshopify.com"]),
//       data: {
//         crispSessionId: "test-session-id",
//         globalId: 1,
//         hello: "world",
//       },
//     },
//   });
//   return shop;
// }

// export async function createShop() {
//   const name = "demo-live.myshopify.com";

//   const shopKey = datastore.client.key([Kind, name]);

//   const shop = {
//     key: shopKey,
//     data: {
//       name: "Abir Shop",
//       domain: "demo-live.myshopify.com",
//     },
//   };

//   await datastore.client.save(shop);
// }

// export async function updateShop(newDat, domain) {
//   const oldData = await datastore.client.get(["Shop", "pial-live.myshopify.com"]);
//   const updatedData = { ...oldData, ...newData };

//   const shop = await datastore.client.update({
//     entity: {
//       key: datastore.client.key([Kind, domain]),
//       data: {
//         ...updatedData,
//       },
//     },
//   });
//   return shop;
// }

// export const getAllShopNames = async () => {
//   const shopNamesSet = new Set();
//   let query = datastore.client.createQuery(Kind).limit(5);
//   let moreResults = true;

//   while (moreResults) {
//     const [shops, info] = await query.run();

//     // Add shop names to the Set
//     shops.forEach((shop) => {
//       const shopKey = get(shop, [datastore.client.KEY]);
//       if (shopKey) shopNamesSet.add({ key: get(shopKey, "name"), shop: shop });
//     });

//     // Check if there are more results and set the cursor for the next query
//     if (info.moreResults !== datastore.client.MORE_RESULTS_AFTER_LIMIT) {
//       moreResults = false;
//     } else if (info.endCursor) {
//       query = query.start(info.endCursor);
//     } else {
//       moreResults = false;
//     }
//   }
//   // Convert Set to Array if needed
//   return Array.from(shopNamesSet);
// };

export const getAllShopNames = async () => {
  const shopNamesSet = new Set();
  let query = datastore.client.createQuery(Kind).limit(750);
  let moreResults = true;

  while (moreResults) {
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.client.KEY]);
      if (shopKey) shopNamesSet.add({ value: get(shopKey, "name"), label: shop.name });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.client.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
};

export const getShopsCount = async () => {
  try {
    // Fetch all shops with a single query
    const allShops = await getAllShopInfo();

    // Initialize counters
    let activeStores = 0;
    let proStores = 0;
    let freeStores = 0;
    let withoutPlan = 0;
    let testStores = 0;
    let inactiveStores = 0;

    // Count different types by iterating through the data
    allShops.forEach((shopItem: any) => {
      const shop = shopItem.info; // The actual shop data is in the 'info' property

      // Count active stores
      if (shop.status === "ACTIVE") {
        activeStores++;
      }

      // Count inactive stores
      if (shop.status === "INACTIVE") {
        inactiveStores++;
      }

      // Count pro stores (with active subscription)
      if (shop.isSubscriptionActive === true) {
        proStores++;
      }

      // Count free stores (without active subscription)
      if (shop.isSubscriptionActive === false) {
        freeStores++;
      }

      // Count stores without plan
      if (shop.appSubscriptionData === null || shop.appSubscriptionData === undefined) {
        withoutPlan++;
      }

      // Count test stores
      if (shop.isPartnerDevelopment === true) {
        testStores++;
      }
    });

    return [
      { title: "Active Stores", value: activeStores },
      { title: "Pro Stores", value: proStores },
      { title: "Free Stores", value: freeStores },
      { title: "Without Plan", value: withoutPlan },
      { title: "Test Stores", value: testStores },
      { title: "Inactive Stores", value: inactiveStores }
    ];
  } catch (error) {
    console.error('Error in getShopsCount:', error);

    // Return fallback data when there's an error
    return [
      { title: "Active Stores", value: "N/A" },
      { title: "Pro Stores", value: "N/A" },
      { title: "Free Stores", value: "N/A" },
      { title: "Without Plan", value: "N/A" },
      { title: "Test Stores", value: "N/A" },
      { title: "Inactive Stores", value: "N/A" },
    ];
  }
};

export const getShopsDataByDate = async (startDate: string, endDate: string) => {
  const allShopInfo = await getAllShopInfoByDate(startDate, endDate);

  const proMonthlyStores = allShopInfo
    .filter((shopItem: any) => shopItem.info.isSubscriptionActive === true && (shopItem.info.appSubscriptionData?.planType === "MONTHLY" || shopItem.info.appSubscriptionData?.name === "pro-monthly"))
    .map((shopItem: any) => shopItem.info);

  const proYearlyStores = allShopInfo
    .filter((shopItem: any) => shopItem.info.isSubscriptionActive === true && (shopItem.info.appSubscriptionData?.planType === "YEARLY" || shopItem.info.appSubscriptionData?.name === "pro-yearly"))
    .map((shopItem: any) => shopItem.info);
  const freeStores = allShopInfo.filter((shopItem: any) => shopItem.info.isSubscriptionActive === false).map((shopItem: any) => shopItem.info);

  // // PRO stores query
  // const proStoresQuery = datastore.client
  //   .createQuery(Kind)
    // .filter(
    //   and([
    //     new PropertyFilter("isSubscriptionActive", "=", true),
    //     new PropertyFilter("createAggregationQuerydAt", ">=", startDate),
    //     new PropertyFilter("createdAt", "<=", endDate),
    //   ])
  //   );

  // // Free stores query
  // const freeStoresQuery = datastore.client
  //   .createQuery(Kind)
  //   .filter(
  //     and([
  //       new PropertyFilter("isSubscriptionActive", "=", false),
  //       new PropertyFilter("createdAt", ">=", startDate),
  //       new PropertyFilter("createdAt", "<=", endDate),
  //     ])
  //   );

  // const [[proStores], [freeStores]] = await Promise.all([
  //   await datastore.client.runQuery(proStoresQuery),
  //   await datastore.client.runQuery(freeStoresQuery),
  // ]);
  
  return [...proMonthlyStores, ...proYearlyStores, ...freeStores];
};

export const getShopDetails = async (domain: string) => {
  if (domain) {
    const key = datastore.client.key([Kind, domain]);
    const [result] = await datastore.client.get(key);
    return result;
  }
  return null;
};

/**
 * Function to get all unique shop names from Datastore
 * returns {Promise<string[]>} Array of unique shop names
 */
export const getAllShopInfo = async() => {
  const shopNamesSet = new Set();
  let query = datastore.client.createQuery("Shop").limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.client.KEY]);
      if (shopKey) shopNamesSet.add({ key: get(shopKey, "name"), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.client.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
}

export const getAllShopInfoByDate = async(startDate: string, endDate: string) => {
  const shopNamesSet = new Set();
  let query = datastore.client.createQuery("Shop")
  .filter(
    and([
      new PropertyFilter("createdAt", ">=", startDate),
      new PropertyFilter("createdAt", "<=", endDate),
    ])
  )
  .limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.client.KEY]);
      if (shopKey) shopNamesSet.add({ key: get(shopKey, "name"), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.client.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
}


