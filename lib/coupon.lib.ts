import { IAddCoupon, IUpdateCoupon } from "@/types/coupons";
import { serializePrismaResponse } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";
import { prisma } from "./db";

export async function getCoupons(search?: string, sortBy: string = "created_at", sortOrder: string = "DESC") {
  const whereClause: Prisma.couponWhereInput = search
    ? {
        OR: [{ name: { contains: search, mode: "insensitive" } }, { code: { contains: search, mode: "insensitive" } }],
      }
    : {};

  const count = await prisma.coupon.count({ where: whereClause });

  const response = await prisma.coupon.findMany({
    where: whereClause,
    orderBy: { [sortBy]: sortOrder.toLocaleLowerCase() },
  });

  const coupons = serializePrismaResponse(response);

  return { coupons, count };
}

export async function getCoupon(id: number) {
  const response = await prisma.coupon.findUnique({ where: { id: id } });
  const coupon = serializePrismaResponse(response);
  return coupon;
}

export async function addCoupon(coupon: IAddCoupon) {
  return await prisma.coupon.create({
    data: coupon,
  });
}

export async function deleteCoupon(id: number) {
  return await prisma.coupon.delete({ where: { id: id } });
}

export async function updateCoupon(id: number, data: IUpdateCoupon) {
  return await prisma.coupon.update({
    where: { id: id },
    data: data,
  });
}
