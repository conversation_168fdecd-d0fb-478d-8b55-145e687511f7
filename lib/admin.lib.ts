import { roles } from "@/enums/admins";
import { prisma } from "@/lib/db";
import { TAdmin } from "@/types/admin";
import { exclude } from "@/utlis/helpers";
import { Prisma } from "@prisma/client";

export async function getAdmins(search?: string) {
  const whereClause: Prisma.adminWhereInput = search
    ? {
        OR: [{ name: { contains: search, mode: "insensitive" } }, { email: { contains: search, mode: "insensitive" } }],
      }
    : {};

  const admins = await prisma.admin.findMany({ where: whereClause });

  const roleMapping = Object.fromEntries(Object.entries(roles).map(([key, value]) => [value, key]));

  return admins?.map((admin) => ({
    ...exclude(admin, ["password"]),
    role: roleMapping[admin.type],
  })) as TAdmin[];
}

export async function getAdmin(id: number) {
  return await prisma.admin.findUnique({ where: { id: id } });
}

export async function addAdmin(
  name: string,
  email: string,
  password: string,
  type: number,
  scopes: string | undefined
) {
  return await prisma.admin.create({
    data: {
      name,
      email,
      password,
      scopes: scopes || "[]",
      type,
    },
  });
}

export async function deleteAdmin(id: number) {
  return await prisma.admin.delete({ where: { id: id } });
}

export async function updateAdmin(id: number, name: string, email: string, type: number) {
  return await prisma.admin.update({
    where: { id: id },
    data: {
      name,
      email,
      type,
    },
  });
}

export async function updatePassword(id: number, password: string) {
  return await prisma.admin.update({
    where: { id: id },
    data: {
      password: password,
    },
  });
}

export async function updatePermission(id: number, data: string[]) {
  return await prisma.admin.update({
    where: { id: id },
    data: {
      scopes: JSON.stringify(data),
    },
  });
}
