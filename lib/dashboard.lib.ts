import { planCategories } from "@/enums";
import { IStoresCountByDate, IStoresCountByHour, IStoresCountByMonth, ITimePeriodData } from "@/types/dashboard";
import { IShopData } from "@/types/stores";
import { formatCustomDate } from "@/utlis/helpers";
import dayjs from "dayjs";
import datastore from "./datastore";

const planTypes = [planCategories.FREE, planCategories.PRO_MONTHLY, planCategories.PRO_YEARLY];

const monthOrder = {
  Jan: 0,
  Feb: 1,
  Mar: 2,
  Apr: 3,
  May: 4,
  Jun: 5,
  Jul: 6,
  Aug: 7,
  Sep: 8,
  Oct: 9,
  Nov: 10,
  Dec: 11,
};

export const generateTimePeriod = (start: string, end: string, unitOfTime: dayjs.ManipulateType = "days") => {
  const startDate = dayjs(start).startOf("day");
  const endDate = dayjs(end).endOf("day");

  const diff = endDate.diff(startDate, unitOfTime);

  return Array.from({ length: diff + 1 }, (_, index) => startDate.add(index, unitOfTime));
};

export const getStoresCountByDate = async (stores: IShopData[]) => {
  const storeCounts: ITimePeriodData = {};

  stores.forEach((store: IShopData) => {
    const storeDate = dayjs(store.createdAt).format("YYYY-MM-DD");
    const category = store?.isSubscriptionActive ? (store?.planSlug === "pro-yearly" ? "PRO_YEARLY" : "PRO_MONTHLY") : "FREE";

    // Initialize the count object for this date if it doesn't exist
    if (!storeCounts[storeDate]) {
      storeCounts[storeDate] = { FREE: 0, PRO_MONTHLY: 0, PRO_YEARLY: 0 };
    }

    // Increment the count for the category (PRO or FREE)
    storeCounts[storeDate][category] += 1;
  });

  const result = [];
  for (const date in storeCounts) {
    if (storeCounts.hasOwnProperty(date)) {
      result.push(
        { date, category: "FREE", count: storeCounts[date].FREE },
        { date, category: "PRO_MONTHLY", count: storeCounts[date].PRO_MONTHLY },
        { date, category: "PRO_YEARLY", count: storeCounts[date].PRO_YEARLY }
      );
    }
  }
  return result.sort((a, b) => (a.date > b.date ? 1 : -1));
};

export const getStoresCountByHours = async (stores: IShopData[]) => {
  const storeCounts: ITimePeriodData = {};

  stores.forEach((store: IShopData) => {
    const storeDate = dayjs(store.createdAt).format("hh:mm a");
    const category = store?.isSubscriptionActive ? (store?.planSlug === "pro-yearly" ? "PRO_YEARLY" : "PRO_MONTHLY") : "FREE";

    // Initialize the count object for this date if it doesn't exist
    if (!storeCounts[storeDate]) {
      storeCounts[storeDate] = { FREE: 0, PRO_MONTHLY: 0, PRO_YEARLY: 0 };
    }

    // Increment the count for the category (PRO or FREE)
    storeCounts[storeDate][category] += 1;
  });

  const result = [];
  for (const hour in storeCounts) {
    if (storeCounts.hasOwnProperty(hour)) {
      result.push(
        { hour, category: "FREE", count: storeCounts[hour].FREE },
        { hour, category: "PRO_MONTHLY", count: storeCounts[hour].PRO_MONTHLY },
        { hour, category: "PRO_YEARLY", count: storeCounts[hour].PRO_YEARLY }
      );
    }
  }
  return result.sort((a, b) => (a.hour > b.hour ? 1 : -1));
};

export const getStoresCountByYear = async (stores: IShopData[]) => {
  const storeCounts: Record<string, { FREE: number; PRO_MONTHLY: number, PRO_YEARLY: number }> = {};

  stores.forEach((store: IShopData) => {
    const storeDate = dayjs(store.createdAt).startOf("month").format("YYYY-MM-DD");
    const category = store?.isSubscriptionActive ? (store?.planSlug === "pro-yearly" ? "PRO_YEARLY" : "PRO_MONTHLY") : "FREE";

    if (!storeCounts[storeDate]) {
      storeCounts[storeDate] = { FREE: 0, PRO_MONTHLY: 0 , PRO_YEARLY: 0 };
    }

    storeCounts[storeDate][category]++;
  });

  // Convert counts to an array of objects
  const result = Object.entries(storeCounts).flatMap(([month, counts]) => [
    { month, category: "FREE", count: counts.FREE },
    { month, category: "PRO_MONTHLY", count: counts.PRO_MONTHLY },
    { month, category: "PRO_YEARLY", count: counts.PRO_YEARLY },
  ]);

  return result.sort((a, b) => (a.month > b.month ? 1 : -1));
};

export const generateMonthlyChartSeries = (storesCount: IStoresCountByDate[], timePeriod: dayjs.Dayjs[]) => {
  const dates = timePeriod.map((date) => dayjs(date).format("YYYY-MM-DD"));

  const series = planTypes.map((category) => {
    const categoryData = storesCount.filter((item: IStoresCountByDate) => item.category === category);

    // Push missing dates with 0 count
    dates.forEach((date: string) => {
      const found = categoryData.find((item: IStoresCountByDate) => item.date === date);
      if (!found) {
        categoryData.push({ date, category, count: 0 });
      }
    });

    categoryData.sort((a: IStoresCountByDate, b: IStoresCountByDate) => (a.date > b.date ? 1 : -1));

    const data = categoryData.map((item: IStoresCountByDate) => ({
      x: dayjs(item.date).format("DD MMM"),
      y: item.count,
    }));

    return {
      name: category,
      data,
    };
  });

  return series;
};

export const generateDailyChartSeries = (counts: IStoresCountByHour[], timePeriod: dayjs.Dayjs[]) => {
  const series = planTypes.map((category) => {
    const categoryData = counts.filter((item: IStoresCountByHour) => item.category === category);

    // Push missing time with 0 count
    timePeriod.forEach((date) => {
      const found = categoryData.find((item) => item.hour === dayjs(date).format("hh:mm a"));
      if (!found) {
        categoryData.push({ hour: dayjs(date).format("hh:mm a"), category, count: 0 });
      }
    });

    // Sort by date
    function compareTime(a: string, b: string) {
      // Parse the time strings into Date objects for comparison
      var timeA = new Date("2000/01/01 " + a);
      var timeB = new Date("2000/01/01 " + b);

      // Compare the time objects
      return timeA.getTime() - timeB.getTime();
    }
    categoryData.sort((a, b) => compareTime(a.hour, b.hour));

    const data = categoryData.map((item) => ({
      x: item.hour,
      y: item.count,
    }));

    return {
      name: category,
      data,
    };
  });

  return series;
};

export const generateYearlyChartSeries = (counts: IStoresCountByMonth[], timePeriod: dayjs.Dayjs[]) => {
  const series = planTypes.map((category) => {
    // Filter data for the current category
    const categoryData = counts.filter((item) => item.category === category);

    // Use a Map to track data for each month
    const monthMap = new Map<string, number>();

    // Populate the Map with existing data
    categoryData.forEach((item) => {
      const monthName = dayjs(item.month).format("MMM");
      monthMap.set(monthName, item.count);
    });

    // Ensure all months are covered
    timePeriod.forEach((date) => {
      const monthName = date.format("MMM");
      if (!monthMap.has(monthName)) {
        monthMap.set(monthName, 0);
      }
    });

    // Create sorted data array
    const data = Array.from(monthMap.entries())
      .sort(
        ([monthA], [monthB]) =>
          monthOrder[monthA as keyof typeof monthOrder] - monthOrder[monthB as keyof typeof monthOrder]
      )
      .map(([month, count]) => ({ x: month, y: count }));

    return {
      name: category,
      data,
    };
  });

  return series;
};

export const getAllChartReport = async (start: any, end: any) => {
  if (!start || !end) {
    // If start and end date is not provided, then default to the first and current date of the month
    start = dayjs().startOf("month");
    end = dayjs().endOf("day");
  }
  const timePeriod = generateTimePeriod(start, end);

  if (start && end && start === end) {
    // daily active installation report grouped by 24 hours
    return await getDailyChartReport(start);
  } else if (timePeriod.length > 31) {
    // yearly active installation report grouped by 12 months
    return await getYearlyChartReport(start, end);
  } else {
    // monthly active installation report grouped by days
    return await getMonthlyChartReport(start, end);
  }
};

export const getMonthlyChartReport = async (start: string, end: string) => {
  const startDate = formatCustomDate(start);
  const endDate = formatCustomDate(end, 23); // Set end time to 23:59:59 to include full day
  const timePeriod = generateTimePeriod(start, end);
  const stores = await datastore.shop.getShopsDataByDate(startDate, endDate);
  const storesCount = await getStoresCountByDate(stores);
  const series = generateMonthlyChartSeries(storesCount, timePeriod);
  return series;
};

export const getDailyChartReport = async (start: string) => {
  const startDate = formatCustomDate(start);
  const endDate = formatCustomDate(start, 23);
  const timePeriod = generateTimePeriod(start, start, "hours");
  const stores = await datastore.shop.getShopsDataByDate(startDate, endDate);
  const storesCount = await getStoresCountByHours(stores);
  const series = generateDailyChartSeries(storesCount, timePeriod);
  return series;
};

export const getYearlyChartReport = async (start: string, end: string) => {
  const startDate = formatCustomDate(start);
  const endDate = formatCustomDate(end, 23); // Set end time to 23:59:59 to include full day
  const timePeriod = generateTimePeriod(start, end, "months");
  const stores = await datastore.shop.getShopsDataByDate(startDate, endDate);
  const storesCount = await getStoresCountByYear(stores);
  const series = generateYearlyChartSeries(storesCount, timePeriod);
  return series;
};
