"server-only";

import { SignJWT, jwtVerify } from "jose";
import { cookies } from "next/headers";
import { prisma } from "./db";

const secretKey = process.env.ACCESS_TOKEN_SECRET;
const encodedKey = new TextEncoder().encode(secretKey);

interface IUserPayload {
  email: string;
}

export async function encrypt(payload: Record<string, string>) {
  try {
    const jwt = await new SignJWT(payload)
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt()
      .setExpirationTime(process.env.ACCESS_TOKEN_EXPIRATION as string)
      .sign(encodedKey);

    return jwt;
  } catch (error) {
    throw new Error("Failed to encrypt payload");
  }
}

export async function decrypt<T extends Record<string, any>>(session: string | undefined): Promise<T | null> {
  if (!session) {
    return null;
  }

  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ["HS256"],
    });

    return payload as T;
  } catch (error) {
    throw new Error("Failed to verify session");
  }
}

export async function createSession(id: number, email: string, scopes: string) {
  const stringId = id.toString();
  const expiresAt = new Date(Date.now() + (process.env.COOKIE_EXPIRATION as string));
  const session = await encrypt({ stringId, email, scopes: JSON.parse(scopes) });
  (await cookies()).set("session", session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: "lax",
    path: "/",
  });
}

export async function updateSession(email?: string, scopes?: string) {
  const session = (await cookies()).get("session")?.value;
  const payload = await decrypt(session);

  if (!session || !payload) {
    return null;
  }

  const expiresAt = new Date(Date.now() + (process.env.COOKIE_EXPIRATION as string));

  const updatedSession = await encrypt({
    ...payload,
    email: email ? email : payload.email,
    scopes: scopes ? scopes : payload.scopes,
  });

  const cookieStore = await cookies();
  cookieStore.set("session", updatedSession, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: "lax",
    path: "/",
  });
}

export async function getUser() {
  const storedCookies = await cookies();
  const session = storedCookies.get("session")?.value;

  const userData = await decrypt<IUserPayload>(session);

  // if (isEmpty(session)) {
  //   return null;
  // }

  if (!session || (userData && Object.keys(userData).length === 0)) {
    return null;
  }

  const user = await prisma.admin.findUnique({
    where: { email: userData?.email },
    select: { id: true, name: true, email: true, type: true, scopes: true },
  });

  return user;
}
