/* eslint-disable no-unused-vars */
type Plans = "FREE" | "PRO";
type Intervals = "MONTHLY" | "ANNUALLY" | "LIFETIME" | "USAGE" | "VISIONARY";
type Status = "ACTIVE" | "INACTIVE";
type Discount = "AMOUNT" | "PERCENT";
type Features =
  | "allOptionTypes"
  | "unlimitedVariantsOptions"
  | "conditionalLogic"
  | "bulkEditApply"
  | "liveChat"
  | "removeWatermark";

export const PLAN_TYPES: { [x in Plans]: { value: string; label: string } } = {
  FREE: {
    value: "free",
    label: "Free",
  },
  PRO: {
    value: "pro",
    label: "Pro",
  },
};

export const PLAN_INTERVALS: { [x in Intervals]: { value: string; label: string } } = {
  MONTHLY: {
    value: "monthly",
    label: "Monthly",
  },

  ANNUALLY: {
    value: "yearly",
    label: "Yearly",
  },

  VISIONARY: {
    value: "visionary",
    label: "Visionary",
  },

  LIFETIME: {
    value: "lifetime",
    label: "Lifetime",
  },

  USAGE: {
    value: "usage",
    label: "Usage",
  },
};

export const STATUS: { [x in Status]: { value: boolean; label: string } } = {
  ACTIVE: {
    value: true,
    label: "Active",
  },
  INACTIVE: {
    value: false,
    label: "Inactive",
  },
};

export const DISCOUNT_TYPE: { [x in Discount]: { value: string; label: string } } = {
  AMOUNT: {
    value: "amount",
    label: "Amount",
  },
  PERCENT: {
    value: "percent",
    label: "Percent",
  },
};

const FEATURES: { [x in Features]: { value: boolean; label: string; name: string } } = {
  allOptionTypes: {
    label: "All option types (except File Upload)",
    name: "all_option_types_without_file_upload",
    value: true,
  },
  unlimitedVariantsOptions: {
    label: "Unlimited variants options",
    name: "unlimited_variants_options",
    value: true,
  },
  conditionalLogic: {
    name: "conditional_logic",
    value: true,
    label: "Conditional logic",
  },
  bulkEditApply: {
    label: "Bulk edit/apply",
    name: "bulk_edit_apply",
    value: true,
  },
  liveChat: {
    label: "Live chat",
    name: "live_chat",
    value: true,
  },
  removeWatermark: {
    label: "Remove watermark",
    name: "remove_watermark",
    value: true,
  },
};

export const planType = Object.entries(PLAN_TYPES).map(([, value]) => ({
  value: value.value,
  label: value.label,
}));

export const planInterval = Object.entries(PLAN_INTERVALS).map(([, value]) => ({
  value: value.value,
  label: value.label,
}));

export const status = Object.entries(STATUS).map(([, value]) => ({
  value: String(value.value),
  label: value.label,
}));

export const discountType = Object.entries(DISCOUNT_TYPE).map(([, value]) => ({
  value: value.value,
  label: value.label,
}));

export const features = Object.entries(FEATURES).map(([, value]) => ({
  value: value.value,
  display_name: value.label,
  name: value.name,
}));

export const meta = {
  duration: 1,
  description: "",
  terms: "", // usage charge plan
  coupon_code: null,
  capped_amount: null,
};
