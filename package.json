{"name": "easyflow-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8000", "lint": "next lint", "format": "prettier --write .", "prepare": "husky", "db:seed": "ts-node prisma/seeder/index.ts"}, "dependencies": {"@google-cloud/datastore": "^9.2.1", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@jridgewell/gen-mapping": "0.3.4", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "apexcharts": "^4.2.0", "axios": "^1.7.8", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.461.0", "next": "15.0.3", "prisma": "^5.22.0", "react": "19.0", "react-apexcharts": "^1.7.0", "react-day-picker": "8.10.1", "react-dom": "19.0", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "server-only": "^0.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/react": "^19.0", "@types/react-dom": "^19.0", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}