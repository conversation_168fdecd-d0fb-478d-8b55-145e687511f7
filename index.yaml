indexes:
  - kind: Shop
    properties:
      - name: __key__
      - name: globalId
      - name: __key__
  - kind: Option set
    ancestor: yes
    properties:
      - name: createdAt
        direction: desc
  - kind: Option set
    properties:
      - name: __key__
      - name: productIds
  - kind: Option set
    properties:
      - name: __key__
      - name: optionIds
      - name: productIds
  - kind: Option set
    ancestor: yes
    properties:
      - name: optionIds
      - name: productIds
  - kind: Option set
    properties:
      - name: optionIds
      - name: productIds
  - kind: Option set
    ancestor: yes
    properties:
      - name: __key__
      - name: productIds
      - name: productSelectionMethod
  - kind: Option set
    properties:
      - name: productSelectionMethod
      - name: optionIds
      - name: productSelectionValues
  - kind: Option set
    properties:
      - name: productSelectionMethod
      - name: optionIds
      - name: productSelectionValues
      - name: rules
  - kind: Option set
    ancestor: yes
    properties:
      - name: productSelectionMethod
  - kind: Option set
    ancestor: yes
    properties:
      - name: __key__
      - name: productSelectionMethod

  - kind: Option
    ancestor: yes
    properties:
      - name: createdAt
        direction: desc
  - kind: Option
    properties:
      - name: __key__
      - name: metaobjectId
  - kind: Option
    properties:
      - name: __key__
      - name: metaobjectId
      - name: productIds
  - kind: Option
    ancestor: yes
    properties:
      - name: __key__
      - name: values.image.fileId
  - kind: Option
    properties:
      - name: __key__
      - name: __key__
      - name: values.image.fileId
  - kind: Option
    properties:
      - name: values.image.fileId
      - name: values.image
  - kind: Option
    properties:
      - name: __key__
      - name: values.addonProduct.id
  - kind: Shop
    properties:
      - name: status
      - name: name
  - kind: Shop
    properties:
      - name: email
      - name: createdAt
        direction: desc
  - kind: Shop
    properties:
      - name: domain
      - name: createdAt
        direction: desc
  - kind: Shop
    properties:
      - name: status
      - name: createdAt
        direction: desc
  - kind: Option set
    ancestor: yes
    properties:
      - name: title
        direction: desc
  - kind: Shop
    properties:
      - name: isSubscriptionActive
      - name: createdAt