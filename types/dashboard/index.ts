export interface ISeries {
  name: string;
  data: ISeriesData[];
}

export interface ISeriesData {
  x: string;
  y: number | string;
}

export interface IPlanCounts {
  FREE: number;
  PRO_MONTHLY: number;
  PRO_YEARLY: number;
}

export interface ITimePeriodData {
  [date: string]: IPlanCounts;
}

export interface IChartData {
  series: ISeries[];
  options: IChartOptions;
}

export interface IChartOptions {
  chart: {
    type: TChartType;
    height: number;
    stacked: boolean;
    toolbar: {
      show: boolean;
    };
    colors: string[];
    animations: {
      enabled: boolean;
      easing: string;
      speed: number;
      animateGradually: {
        enabled: boolean;
        delay: number;
      };
      dynamicAnimation: {
        enabled: boolean;
        speed: number;
      };
    };
  };
  responsive: [
    {
      options: {
        legend: {
          position: string;
          offsetX: number;
          offsetY: number;
        };
      };
    },
  ];
  xaxis: {
    labels: {
      show: boolean;
      rotate: number;
      rotateAlways: boolean;
      minHeight: number;
      maxHeight: number;
    };
    type: TXAxisType;
  };
  legend: {
    show: boolean;
    position: TChartPosition;
    markers: {
      shape: ApexMarkerShape;
    };
  };
  fill: {
    opacity: number;
  };
}

export type TChartPosition = "top" | "right" | "bottom" | "left";
export type TXAxisType = "category" | "datetime" | "numeric";

export type TChartType =
  | "line"
  | "area"
  | "bar"
  | "pie"
  | "donut"
  | "radialBar"
  | "scatter"
  | "bubble"
  | "heatmap"
  | "candlestick"
  | "boxPlot"
  | "radar"
  | "polarArea"
  | "rangeBar"
  | "rangeArea"
  | "treemap";

export interface IStoresCountByDate {
  date: string;
  category: string;
  count: number;
}

export interface IStoresCountByHour {
  hour: string;
  category: string;
  count: number;
}

export interface IStoresCountByMonth {
  month: string;
  category: string;
  count: number;
}
