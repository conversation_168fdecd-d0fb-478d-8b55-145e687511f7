export type TAdmin = {
  id: number;
  name: string;
  email: string;
  type: string;
  scopes: string;
  role: string;
  created_at?: string | Date;
  updated_at?: string | Date;
};

export type TUpdateAdmin = {
  name: string;
  email: string;
  type: null | number;
  scopes?: string | string[] | null;
};

export type TAdminPassword = {
  password: string;
  confirmPassword: string;
};

export type TAddAdmin = TUpdateAdmin & TAdminPassword;

export type TPermissionScopes = Record<
  string,
  {
    code: string;
    description: string;
  }
>;

export type TPermission = {
  name: string;
  features: string[];
};
