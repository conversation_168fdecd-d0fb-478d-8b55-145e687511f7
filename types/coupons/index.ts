/* eslint-disable no-unused-vars */
export interface ICouponData {
  id: number;
  name: string;
  code: string;
  discount_type: "percent" | "amount";
  amount: number;
  discount_limit_duration: number;
  start_date: string;
  end_date: string;
  max_limit: number;
  redeem_count: number;
  plans: string[];
  status: boolean;
  created_at: string;
  updated_at: string;
}

export interface ICoupon {
  count: number;
  coupons: ICouponData[];
}

export interface ICouponModalProps {
  title: string;
  primaryActionText: string;
  openModal: boolean;
  setOpenModal: (openModal: boolean) => void;
  coupon?: ICouponData;
  isEditing?: boolean;
  isCopied?: boolean;
}

export interface IAddCoupon {
  id?: number;
  name: string;
  code: string;
  amount: number;
  discount_type: "percent" | "amount";
  discount_limit_duration?: number;
  start_date: string | Date;
  end_date: string | Date;
  max_limit: number;
  plans?: string[];
  status: boolean;
}

export type IUpdateCoupon = Omit<IAddCoupon, "code" | "amount" | "discount_type">;
