/* eslint-disable no-unused-vars */
export interface ISubscriptionPlanData {
  id: number;
  name: string;
  slug: string;
  status: boolean;
  type: string;
  interval: string;
  package_info: IPlanPackageInfo[];
  meta: IPlanMeta;
  trial_days: number;
  currency: string;
  price_save_description: string;
  coupon_id: number | null;
  test: boolean;
  price: number;
  discount_type: "amount" | "percent";
  discount: number;
  final_price: number;
  plan_features: string[];
  created_at: string;
  updated_at: string;
}

export interface IPlanMeta {
  terms: string;
  duration: number;
  description: string;
  capped_amount: { amount: number; currencyCode: string; price_save_description?: string } | null;
}

export interface IPlanPackageInfo {
  value: boolean;
  display_name: string;
  name: string;
}

export interface ISubscriptionModal {
  title: string;
  primaryActionText: string;
  openModal: boolean;
  plan?: ISubscriptionPlanData;
  setOpenModal: (openModal: boolean) => void;
  isEditing?: boolean;
  isCopied?: boolean;
}

export interface IAddSubscriptionPlan {
  id?: number;
  name: string;
  slug: string;
  status: boolean;
  type: string;
  interval: string;
  trial_days: number;
  currency: string;
  test: boolean;
  price: number;
  final_price: number;
  meta?:
    | {
        terms: string;
        duration: number;
        description: string;
        capped_amount: { amount: number; currencyCode: string; price_save_description?: string } | null;
      }
    | undefined;
  package_info?: { name: string; value: boolean; display_name: string }[];
  coupon_id?: number | null;
  discount_type?: "percent" | "amount" | null;
  discount?: number;
  price_save_description?: string;
}
