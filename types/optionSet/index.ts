export interface IShopNames {
  label: string;
  value: string;
}

export interface IOptionSetParams {
  search: string;
  sortBy: string;
  sortOrder: string;
  page: string;
  limit: string;
  shop: string;
}

export interface IOptionSet {
  optionSet: IOptionSetData[];
  totalCount: number;
  optionSetCount: number;
}

export interface IOptionSetData {
  title?: string;
  rules: string[];
  productSelectionMethod: string;
  productIds: string[];
  rank: string;
  optionIds: string[];
  createdAt: string;
  domain: string;
  id: string;
}

export interface IOptionData {
  title: string;
  type: string;
  values?: IOptionValues[];
  productIds: string[];
  isMultiSelect: boolean;
  isShowSelectedValues: boolean;
  isRequired: boolean;
  metaobjectId: string;
  createdAt: string;
}

export interface IOptionValues {
  title: string;
  price: string;
  isDefault: boolean;
  addonType: string;
  addonProduct?: {
    id: string;
    handle: string;
    variantId: string;
  };
}

export interface IOptionRules {
  actions: IOptionRulesActions[];
  condiitons: IOptionConditons[];
  name: string;
  isAllConditionsRequired?: boolean;
}

interface IOptionRulesActions {
  optionId: string;
  type: "hide" | "show";
  values: string[];
}

interface IOptionConditons {
  optionId: string;
  relation: "in";
  values: string[];
}

export interface IOptionSetStats {
  total: number;
}

export interface IOptionStats {
  total: number;
  withPrice: number;
  withoutPrice: number;
}
