import { JsonValue } from "@prisma/client/runtime/library";

export interface IShopData {
  billingAddress?: IBillingdata;
  billingConfigKey: string;
  createdAt: string;
  crispSessionId: string;
  currencyCode: string;
  currencyFormats?: {
    moneyWithCurrencyFormat: string;
  };
  domain: string;
  email: string;
  globalId: string;
  isAddonsFree: boolean;
  isExtensionEnabled: boolean;
  isPartnerDevelopment: boolean;
  isShowBranding: boolean;
  isSubscriptionActive: boolean;
  name: string;
  phone: string;
  shopifyPlan: {
    displayName: string;
    partnerDevelopment: string;
    shopifyPlus: string;
  };

  // Plan-related fields
  planData?: {
    type: string;
    trialDays: string;
    slug: string;
    price: number;
    name: string;
    meta: {
      terms: string;
      price_save_description: string;
      duration: string;
      description: string;
      coupon_code: string | null;
      capped_amount: string | null;
    };
    interval: string;
    finalPrice: number;
    duration: string;
    discount: string;
    cappedAmount: string | null;
  };
  planExpireDate?: string;
  planFeatures?: {
    [key: string]: boolean;
  };
  planSlug?: string;
  planStatus?: string;
  subscriptionPlan?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;

  url: string;
  status: string;
}

export interface IBillingdata {
  address1: string;
  address2: string;
  city: string;
  company: string;
  country: string;
  countryCodeV2: string;
  formatted: string[];
  formattedArea: string;
  id: string;
  latitude: string;
  longitude: string;
  phone: string;
  province: string;
  provinceCode: string;
  zip: string;
}

export interface IStores {
  stores: IShopData[];
  totalCount: number;
  shopsCount: number;
}

export interface IWebhookData {
  id: bigint;
  shop_domain: string;
  wh_subs_id: string;
  topic: string;
  delivery_method: string;
  response: JsonValue;
  created_at: Date;
  updated_at: Date;
}
