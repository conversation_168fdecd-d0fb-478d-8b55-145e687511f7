export interface INavBar {
  name: string;
  href: string;
  icon?: React.ElementType | React.ReactNode;
  current: boolean;
  requiredScopes?: string[];
}

export interface INavBarWithChild extends INavBar {
  child?: INavBar[];
}

export interface LoginFormValues {
  email: string;
  password: string;
}

export interface ICookie {
  key: string;
  value: string;
  options: {
    httpOnly: boolean;
    secure: boolean;
    sameSite: "none" | "lax" | "strict";
    maxAge: number;
    path: string;
  };
}

export interface ISearchFilter {
  tableFilterOpen: boolean;
  setTableFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onManageModal: () => void;
  onClearQuery: () => void;
  onStatus?: () => void;
}
