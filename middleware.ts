import { NextRequest, NextResponse } from "next/server";
import { decrypt } from "./lib/session";
import { scopes } from "./modules/mappers/adminRolesPermission";

const protectedRoutes = [
  { path: "/dashboard", permissions: scopes.DASHBOARD.adminPermissons },
  { path: "/stores", permissions: scopes.STORE.adminPermissons },
  { path: "/option-set", permissions: scopes.OPTION_SET.adminPermissons },
  { path: "/settings/subscription-plans", permissions: scopes.SUBSCRIPTION_PLAN.adminPermissons },
  { path: "/settings/coupons", permissions: scopes.COUPON.adminPermissons },
  // { path: "/settings/admins", permissions: scopes.ADMIN.adminPermissons },
];

const publicRoutes = ["/login"];

export default async function middleware(req: NextRequest) {
  const currentpath = req.nextUrl.pathname;
  const activeRoute = protectedRoutes?.find((route) => currentpath?.startsWith(route?.path));

  const isProtectedRoute = protectedRoutes.some((route) => currentpath.startsWith(route.path));
  const isPublicRoute = publicRoutes.includes(currentpath);

  try {
    const cookies = req.cookies.get("session")?.value || "";
    const session = await decrypt(cookies);

    if (currentpath === "/") {
      return NextResponse.redirect(new URL("/dashboard", req.nextUrl));
    }

    if (isProtectedRoute && !session) {
      return NextResponse.redirect(new URL("/login", req.nextUrl));
    }

    if (isPublicRoute && session) {
      return NextResponse.redirect(new URL("/dashboard", req.nextUrl));
    }

    if (activeRoute) {
      const hasPermission = activeRoute?.permissions?.some((permission) => session?.scopes?.includes(permission));
      if (!hasPermission) {
        return NextResponse.redirect(new URL("/unauthorized", req.url));
      }
    }
  } catch (error) {
    throw new Error(`Error: ${error}`);
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|.*\\.png$).*)"],
};
